{"include": ["app"], "exclude": ["**/__pycache__"], "venvPath": ".", "venv": ".venv", "typeCheckingMode": "strict", "reportMissingImports": true, "reportMissingTypeStubs": false, "reportOptionalMemberAccess": false, "reportOptionalSubscript": false, "reportOptionalIterable": false, "reportOptionalContextManager": false, "reportOptionalOperand": false, "reportGeneralTypeIssues": "warning", "reportIncompatibleMethodOverride": "warning", "reportIncompatibleVariableOverride": "warning", "reportUnnecessaryIsInstance": false, "reportUnnecessaryComparison": false, "reportCallInDefaultInitializer": false, "reportPropertyTypeMismatch": "warning", "reportFunctionMemberAccess": false, "strictListInference": false, "strictDictionaryInference": false, "strictSetInference": false, "pythonVersion": "3.13", "pythonPlatform": "<PERSON>"}