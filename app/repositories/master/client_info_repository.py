"""Master client info repository for database operations."""

from typing import Annotated, Optional, List, Dict, Any
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import MasterClientInfo
from app.db.session import get_session


class MasterClientInfoRepository:
    """Repository for master client info-related database operations."""

    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        self.db = db

    def get_by_id(self, client_id: int) -> Optional[MasterClientInfo]:
        """Get client by ID."""
        client = self.db.get(MasterClientInfo, client_id)
        return client

    def get_by_name(self, name: str) -> Optional[MasterClientInfo]:
        """Get client by name."""
        statement = select(MasterClientInfo).where(MasterClientInfo.name == name)
        return self.db.exec(statement).first()

    def get_by_email(self, email: str) -> Optional[MasterClientInfo]:
        """Get client by email."""
        statement = select(MasterClientInfo).where(MasterClientInfo.email == email)
        return self.db.exec(statement).first()

    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterClientInfo]:
        """Get all clients with pagination."""
        statement = select(MasterClientInfo).offset(skip).limit(limit)
        return list(self.db.exec(statement).all())

    def create(self, client_data: Dict[str, Any]) -> MasterClientInfo:
        """Create a new client."""
        client = MasterClientInfo(**client_data)
        self.db.add(client)
        self.db.commit()
        self.db.refresh(client)
        return client

    def update(self, client: MasterClientInfo, update_data: Dict[str, Any]) -> MasterClientInfo:
        """Update an existing client."""
        for field, value in update_data.items():
            if hasattr(client, field) and value is not None:
                setattr(client, field, value)
        
        self.db.add(client)
        self.db.commit()
        self.db.refresh(client)
        return client

    def delete(self, client: MasterClientInfo) -> None:
        """Delete a client."""
        self.db.delete(client)
        self.db.commit()

    def toggle_status(self, client: MasterClientInfo) -> MasterClientInfo:
        """Toggle client active status."""
        client.is_active = not client.is_active
        self.db.add(client)
        self.db.commit()
        self.db.refresh(client)
        return client

    def search_by_name(self, name_pattern: str, skip: int = 0, limit: int = 100) -> List[MasterClientInfo]:
        """Search clients by name pattern (simple contains check)."""
        all_clients = self.get_all(skip=0, limit=1000)  # Get more records for filtering
        filtered_clients = [
            client for client in all_clients 
            if name_pattern.lower() in client.name.lower()
        ]
        # Apply pagination to filtered results
        return filtered_clients[skip:skip + limit]

    def get_active_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientInfo]:
        """Get all active clients."""
        statement = select(MasterClientInfo).where(
            MasterClientInfo.is_active == True
        ).offset(skip).limit(limit)
        return list(self.db.exec(statement).all())

    def get_inactive_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientInfo]:
        """Get all inactive clients."""
        statement = select(MasterClientInfo).where(
            MasterClientInfo.is_active == False
        ).offset(skip).limit(limit)
        return list(self.db.exec(statement).all())
