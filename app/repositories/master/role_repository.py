"""Repository for master role management.

This module contains the repository class for master role CRUD operations.
"""

from typing import List, Optional, Annotated
from sqlmodel import Session, select
from fastapi import Depends
from app.db import get_session, MasterRole
from app.schemas.master.role_schema import MasterRoleC<PERSON>, MasterRoleUpdate
from app.core.logs import log_database_error


class RoleRepository:
    """Repository class for master role CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session dependency
        """
        self.db = db
    
    def create(self, role_data: MasterRoleCreate) -> MasterRole:
        """Create a new master role record.
        
        Args:
            role_data: Role data to create
            
        Returns:
            Created role record
            
        Raises:
            Exception: If creation fails
        """
        try:
            role = MasterRole(**role_data.model_dump())
            self.db.add(role)
            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="CREATE",
                table="MasterRole",
                error=e,
                additional_context={"role_data": role_data.model_dump()}
            )
            raise
    
    def get_by_id(self, role_id: int) -> Optional[MasterRole]:
        """Get role by ID.
        
        Args:
            role_id: Role ID
            
        Returns:
            Role record if found, None otherwise
        """
        try:
            statement = select(MasterRole).where(MasterRole.id == role_id)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id}
            )
            return None
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterRole]:
        """Get all role records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of role records
        """
        try:
            statement = select(MasterRole).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, role_id: int, role_data: MasterRoleUpdate) -> Optional[MasterRole]:
        """Update an existing role record.
        
        Args:
            role_id: Role ID to update
            role_data: Updated role data
            
        Returns:
            Updated role record if successful, None otherwise
            
        Raises:
            Exception: If update fails
        """
        try:
            role = self.get_by_id(role_id)
            if not role:
                return None
            
            update_data = role_data.model_dump(exclude_none=True)
            for field, value in update_data.items():
                setattr(role, field, value)
            
            self.db.add(role)
            self.db.commit()
            self.db.refresh(role)
            return role
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="UPDATE",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id, "role_data": role_data.model_dump()}
            )
            raise
    
    def delete(self, role_id: int) -> bool:
        """Delete a role record.
        
        Args:
            role_id: Role ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
            
        Raises:
            Exception: If deletion fails
        """
        try:
            role = self.get_by_id(role_id)
            if not role:
                return False
            
            self.db.delete(role)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                operation="DELETE",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id}
            )
            raise
    
    def check_if_exists(self, role_id: int) -> bool:
        """Check if role exists.
        
        Args:
            role_id: Role ID to check
            
        Returns:
            True if role exists, False otherwise
        """
        try:
            statement = select(MasterRole).where(MasterRole.id == role_id)
            result = self.db.exec(statement)
            return result.first() is not None
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"role_id": role_id, "operation": "check_if_exists"}
            )
            return False
    
    def get_by_name(self, name: str) -> Optional[MasterRole]:
        """Get role by name.
        
        Args:
            name: Role name
            
        Returns:
            Role record if found, None otherwise
        """
        try:
            statement = select(MasterRole).where(MasterRole.name == name)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="MasterRole",
                error=e,
                additional_context={"name": name}
            )
            return None