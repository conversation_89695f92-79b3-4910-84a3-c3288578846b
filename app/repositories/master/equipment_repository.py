"""Repository for master equipment management.

This module contains the repository class for master equipment CRUD operations.
"""

from typing import List, Optional, Annotated
from sqlmodel import Session, select
from fastapi import Depends, HTTPException, status
from app.db import get_session, MasterEquipment
from app.schemas.master.equipment_schema import MasterEquipmentCreate, MasterEquipmentUpdate
from app.core.logs import log_api_error


class EquipmentRepository:
    """Repository class for master equipment CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with database session.
        
        Args:
            db: Database session dependency
        """
        self.db = db
    
    def create(self, equipment_data: MasterEquipmentCreate) -> MasterEquipment:
        """Create a new master equipment record.
        
        Args:
            equipment_data: Equipment data to create
            
        Returns:
            Created equipment record
            
        Raises:
            HTTPException: If creation fails
        """
        try:
            equipment = MasterEquipment(**equipment_data.model_dump())
            self.db.add(equipment)
            self.db.commit()
            self.db.refresh(equipment)
            return equipment
        except Exception as e:
            self.db.rollback()
            log_api_error(
                endpoint="/api/v1/master/equipment",
                method="POST",
                status_code=500,
                error=f"Failed to create equipment: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create equipment"
            )
    
    def get_by_id(self, equipment_id: int) -> Optional[MasterEquipment]:
        """Get equipment by ID.
        
        Args:
            equipment_id: Equipment ID
            
        Returns:
            Equipment record if found, None otherwise
        """
        try:
            statement = select(MasterEquipment).where(MasterEquipment.id == equipment_id)
            result = self.db.exec(statement)
            return result.first()
        except Exception as e:
            log_api_error(
                endpoint=f"/api/v1/master/equipment/{equipment_id}",
                method="GET",
                status_code=500,
                error=f"Failed to get equipment: {str(e)}"
            )
            return None
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[MasterEquipment]:
        """Get all equipment records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of equipment records
        """
        try:
            statement = select(MasterEquipment).offset(skip).limit(limit)
            result = self.db.exec(statement)
            return list(result.all())
        except Exception as e:
            log_api_error(
                endpoint="/api/v1/master/equipment",
                method="GET",
                status_code=500,
                error=f"Failed to get equipment list: {str(e)}"
            )
            return []
    
    def update(self, equipment_id: int, equipment_data: MasterEquipmentUpdate) -> Optional[MasterEquipment]:
        """Update an existing equipment record.
        
        Args:
            equipment_id: Equipment ID to update
            equipment_data: Updated equipment data
            
        Returns:
            Updated equipment record if successful, None otherwise
            
        Raises:
            HTTPException: If update fails
        """
        try:
            equipment = self.get_by_id(equipment_id)
            if not equipment:
                return None
            
            update_data = equipment_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(equipment, field, value)
            
            self.db.add(equipment)
            self.db.commit()
            self.db.refresh(equipment)
            return equipment
        except Exception as e:
            self.db.rollback()
            log_api_error(
                endpoint=f"/api/v1/master/equipment/{equipment_id}",
                method="PUT",
                status_code=500,
                error=f"Failed to update equipment: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update equipment"
            )
    
    def delete(self, equipment_id: int) -> bool:
        """Delete an equipment record.
        
        Args:
            equipment_id: Equipment ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
            
        Raises:
            HTTPException: If deletion fails
        """
        try:
            equipment = self.get_by_id(equipment_id)
            if not equipment:
                return False
            
            self.db.delete(equipment)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_api_error(
                endpoint=f"/api/v1/master/equipment/{equipment_id}",
                method="DELETE",
                status_code=500,
                error=f"Failed to delete equipment: {str(e)}"
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete equipment"
            )
    
    def check_if_exists(self, equipment_id: int) -> bool:
        """Check if equipment exists.
        
        Args:
            equipment_id: Equipment ID to check
            
        Returns:
            True if equipment exists, False otherwise
        """
        try:
            statement = select(MasterEquipment).where(MasterEquipment.id == equipment_id)
            result = self.db.exec(statement)
            return result.first() is not None
        except Exception:
            return False