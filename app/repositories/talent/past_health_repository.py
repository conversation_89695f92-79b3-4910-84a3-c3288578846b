"""Repository for managing talent past health issues data."""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentPastHealthIssues
from app.db.session import get_session


class TalentPastHealthRepository:
    """Repository class for talent past health issues CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_past_health_issue(
        self, past_health_issue: TalentPastHealthIssues
    ) -> TalentPastHealthIssues:
        """Create a new past health issue record.
        
        Args:
            past_health_issue: The past health issue data to create
            
        Returns:
            The created past health issue record
        """
        self.db.add(past_health_issue)
        self.db.commit()
        self.db.refresh(past_health_issue)
        return past_health_issue

    def get_past_health_issues_by_talent_id(
        self, talent_id: int
    ) -> List[TalentPastHealthIssues]:
        """Get all past health issues for a specific talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of past health issue records
        """
        statement = select(TalentPastHealthIssues).where(
            TalentPastHealthIssues.talent_profile_id == talent_id
        )
        return list(self.db.exec(statement).all())

    def get_past_health_issue_by_id(
        self, issue_id: int
    ) -> Optional[TalentPastHealthIssues]:
        """Get a specific past health issue by ID.
        
        Args:
            issue_id: The past health issue ID
            
        Returns:
            The past health issue record if found, None otherwise
        """
        statement = select(TalentPastHealthIssues).where(
            TalentPastHealthIssues.id == issue_id
        )
        return self.db.exec(statement).first()

    def update_past_health_issue(
        self, past_health_issue: TalentPastHealthIssues
    ) -> TalentPastHealthIssues:
        """Update an existing past health issue record.
        
        Args:
            past_health_issue: The updated past health issue data
            
        Returns:
            The updated past health issue record
        """
        self.db.add(past_health_issue)
        self.db.commit()
        self.db.refresh(past_health_issue)
        return past_health_issue

    def delete_past_health_issue(self, issue_id: int) -> bool:
        """Delete a past health issue record.
        
        Args:
            issue_id: The past health issue ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        statement = select(TalentPastHealthIssues).where(
            TalentPastHealthIssues.id == issue_id
        )
        issue = self.db.exec(statement).first()
        if issue:
            self.db.delete(issue)
            self.db.commit()
            return True
        return False
