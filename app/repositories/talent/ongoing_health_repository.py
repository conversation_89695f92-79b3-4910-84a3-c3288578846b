"""Repository for managing talent ongoing health issues data."""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentOngoingHealthIssues
from app.db.session import get_session


class TalentOngoingHealthRepository:
    """Repository class for talent ongoing health issues CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_ongoing_health_issue(
        self, ongoing_health_issue: TalentOngoingHealthIssues
    ) -> TalentOngoingHealthIssues:
        """Create a new ongoing health issue record.
        
        Args:
            ongoing_health_issue: The ongoing health issue data to create
            
        Returns:
            The created ongoing health issue record
        """
        self.db.add(ongoing_health_issue)
        self.db.commit()
        self.db.refresh(ongoing_health_issue)
        return ongoing_health_issue

    def get_ongoing_health_issues_by_talent_id(
        self, talent_id: int
    ) -> List[TalentOngoingHealthIssues]:
        """Get all ongoing health issues for a specific talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of ongoing health issue records
        """
        statement = select(TalentOngoingHealthIssues).where(
            TalentOngoingHealthIssues.talent_profile_id == talent_id
        )
        return list(self.db.exec(statement).all())

    def get_ongoing_health_issue_by_id(
        self, issue_id: int
    ) -> Optional[TalentOngoingHealthIssues]:
        """Get a specific ongoing health issue by ID.
        
        Args:
            issue_id: The ongoing health issue ID
            
        Returns:
            The ongoing health issue record if found, None otherwise
        """
        statement = select(TalentOngoingHealthIssues).where(
            TalentOngoingHealthIssues.id == issue_id
        )
        return self.db.exec(statement).first()

    def update_ongoing_health_issue(
        self, ongoing_health_issue: TalentOngoingHealthIssues
    ) -> TalentOngoingHealthIssues:
        """Update an existing ongoing health issue record.
        
        Args:
            ongoing_health_issue: The updated ongoing health issue data
            
        Returns:
            The updated ongoing health issue record
        """
        self.db.add(ongoing_health_issue)
        self.db.commit()
        self.db.refresh(ongoing_health_issue)
        return ongoing_health_issue

    def delete_ongoing_health_issue(self, issue_id: int) -> bool:
        """Delete an ongoing health issue record.
        
        Args:
            issue_id: The ongoing health issue ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        statement = select(TalentOngoingHealthIssues).where(
            TalentOngoingHealthIssues.id == issue_id
        )
        issue = self.db.exec(statement).first()
        if issue:
            self.db.delete(issue)
            self.db.commit()
            return True
        return False
