"""Vacation mapping repository for talent management.

This module contains the repository class for vacation mapping CRUD operations.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentVacationMapping
from app.db.session import get_session
from app.schemas.talent.vacation_schema import (
    TalentVacationMappingCreate
)
from app.core.logs import log_database_error


class VacationRepository:
    """Repository class for talent vacation mapping operations.
    
    Handles all database operations related to talent vacation mapping.
    """
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db
    
    def create(self, vacation_data: TalentVacationMappingCreate) -> Optional[TalentVacationMapping]:
        """Create a new vacation mapping record.
        
        Args:
            vacation_data: Vacation mapping data to create
            
        Returns:
            Created vacation mapping record or None if creation fails
        """
        try:
            db_vacation = TalentVacationMapping(**vacation_data.model_dump())
            self.db.add(db_vacation)
            self.db.commit()
            self.db.refresh(db_vacation)
            return db_vacation
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="create_vacation_mapping",
                table="TalentVacationMapping",
                additional_context=vacation_data.model_dump()
            )
            return None
    
    def get_by_id(self, vacation_id: int) -> Optional[TalentVacationMapping]:
        """Get vacation mapping by ID.
        
        Args:
            vacation_id: Vacation mapping ID
            
        Returns:
            Vacation mapping record or None if not found
        """
        try:
            statement = select(TalentVacationMapping).where(
                TalentVacationMapping.id == vacation_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_vacation_mapping_by_id",
                table="TalentVacationMapping",
                additional_context={"vacation_id": vacation_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> List[TalentVacationMapping]:
        """Get all vacation mappings for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of vacation mapping records
        """
        try:
            statement = select(TalentVacationMapping).where(
                TalentVacationMapping.talent_profile_id == talent_profile_id
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_vacation_mapping_by_talent_id",
                table="TalentVacationMapping",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return []
    
    def get_by_talent_and_year(self, talent_profile_id: int, year_number: int) -> Optional[TalentVacationMapping]:
        """Get vacation mapping for a specific talent and year.
        
        Args:
            talent_profile_id: Talent profile ID
            year_number: Year number
            
        Returns:
            Vacation mapping record or None if not found
        """
        try:
            statement = select(TalentVacationMapping).where(
                TalentVacationMapping.talent_profile_id == talent_profile_id,
                TalentVacationMapping.year_number == year_number
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_vacation_mapping_by_talent_and_year",
                table="TalentVacationMapping",
                additional_context={
                    "talent_profile_id": talent_profile_id,
                    "year_number": year_number
                }
            )
            return None
    
    def update(self, vacation_id: int, vacation_data: TalentVacationMapping) -> Optional[TalentVacationMapping]:
        """Update vacation mapping.
        
        Args:
            vacation_id: Vacation mapping ID
            vacation_data: Updated vacation mapping data
            
        Returns:
            Updated vacation mapping record or None if update fails
        """
        try:
            db_vacation = self.get_by_id(vacation_id)
            if not db_vacation:
                return None
            
            update_data = vacation_data.model_dump()
            for field, value in update_data.items():
                setattr(db_vacation, field, value)
            
            self.db.add(db_vacation)
            self.db.commit()
            self.db.refresh(db_vacation)
            return db_vacation
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="update_vacation_mapping",
                table="TalentVacationMapping",
                additional_context={
                    "vacation_id": vacation_id,
                    "update_data": vacation_data.model_dump()
                }
            )
            return None
    
    def delete(self, vacation_id: int) -> bool:
        """Delete vacation mapping.
        
        Args:
            vacation_id: Vacation mapping ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_vacation = self.get_by_id(vacation_id)
            if not db_vacation:
                return False
            
            self.db.delete(db_vacation)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="delete_vacation_mapping",
                table="TalentVacationMapping",
                additional_context={"vacation_id": vacation_id}
            )
            return False
    
    def exists_by_talent_and_year(self, talent_profile_id: int, year_number: int) -> bool:
        """Check if vacation mapping exists for a talent and year.
        
        Args:
            talent_profile_id: Talent profile ID
            year_number: Year number to check
            
        Returns:
            True if vacation mapping exists, False otherwise
        """
        try:
            statement = select(TalentVacationMapping).where(
                TalentVacationMapping.talent_profile_id == talent_profile_id,
                TalentVacationMapping.year_number == year_number
            )
            return self.db.exec(statement).first() is not None
        except Exception as e:
            log_database_error(
                error=e,
                operation="check_vacation_mapping_exists",
                table="TalentVacationMapping",
                additional_context={
                    "talent_profile_id": talent_profile_id,
                    "year_number": year_number
                }
            )
            raise e