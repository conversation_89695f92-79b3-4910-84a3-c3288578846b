"""Talent equipment mapping repository for talent management.

This module contains the repository class for talent equipment mapping CRUD operations.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentEquipmentMapping
from app.db.session import get_session
from app.schemas.talent.equipment_mapping_schema import (
    TalentEquipmentMappingCreate
)
from app.core.logs import log_database_error


class TalentEquipmentMappingRepository:
    """Repository class for talent equipment mapping operations.
    
    Handles all database operations related to talent equipment mapping.
    """
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db
    
    def create(self, mapping_data: TalentEquipmentMappingCreate) -> Optional[TalentEquipmentMapping]:
        """Create a new talent equipment mapping record.
        
        Args:
            mapping_data: Equipment mapping data to create
            
        Returns:
            Created equipment mapping record or None if creation fails
        """
        try:
            db_mapping = TalentEquipmentMapping(**mapping_data.model_dump())
            self.db.add(db_mapping)
            self.db.commit()
            self.db.refresh(db_mapping)
            return db_mapping
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="create_talent_equipment_mapping",
                table="TalentEquipmentMapping",
                additional_context=mapping_data.model_dump()
            )
            return None
    
    def get_by_id(self, mapping_id: int) -> Optional[TalentEquipmentMapping]:
        """Get talent equipment mapping by ID.
        
        Args:
            mapping_id: Equipment mapping ID
            
        Returns:
            Equipment mapping record or None if not found
        """
        try:
            statement = select(TalentEquipmentMapping).where(
                TalentEquipmentMapping.id == mapping_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_talent_equipment_mapping_by_id",
                table="TalentEquipmentMapping",
                additional_context={"mapping_id": mapping_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> List[TalentEquipmentMapping]:
        """Get equipment mappings for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of equipment mapping records
        """
        try:
            statement = select(TalentEquipmentMapping).where(
                TalentEquipmentMapping.talent_profile_id == talent_profile_id
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_talent_equipment_mapping_by_talent_id",
                table="TalentEquipmentMapping",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return []
    
    def get_by_equipment_id(self, equipment_id: int) -> List[TalentEquipmentMapping]:
        """Get all talent equipment mappings for a specific equipment.
        
        Args:
            equipment_id: Equipment ID
            
        Returns:
            List of equipment mapping records
        """
        try:
            statement = select(TalentEquipmentMapping).where(
                TalentEquipmentMapping.equipment_id == equipment_id
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_talent_equipment_mapping_by_equipment_id",
                table="TalentEquipmentMapping",
                additional_context={"equipment_id": equipment_id}
            )
            return []
    
    def get_active_mappings(self, skip: int = 0, limit: int = 100) -> List[TalentEquipmentMapping]:
        """Get all active (not replaced) talent equipment mappings with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of active equipment mapping records
        """
        try:
            statement = select(TalentEquipmentMapping).where(
                TalentEquipmentMapping.is_replaced == False
            ).offset(skip).limit(limit)
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_active_talent_equipment_mappings",
                table="TalentEquipmentMapping",
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[TalentEquipmentMapping]:
        """Get all talent equipment mappings with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of equipment mapping records
        """
        try:
            statement = select(TalentEquipmentMapping).offset(skip).limit(limit)
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_all_talent_equipment_mappings",
                table="TalentEquipmentMapping",
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, mapping_id: int, mapping_data: TalentEquipmentMapping) -> Optional[TalentEquipmentMapping]:
        """Update talent equipment mapping.
        
        Args:
            mapping_id: Equipment mapping ID
            mapping_data: Updated equipment mapping data
            
        Returns:
            Updated equipment mapping record or None if update fails
        """
        try:
            db_mapping = self.get_by_id(mapping_id)
            if not db_mapping:
                return None
            
            update_data = mapping_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(db_mapping, field):
                    setattr(db_mapping, field, value)
            
            self.db.add(db_mapping)
            self.db.commit()
            self.db.refresh(db_mapping)
            return db_mapping
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="update_talent_equipment_mapping",
                table="TalentEquipmentMapping",
                additional_context={
                    "mapping_id": mapping_id,
                    "update_data": mapping_data.model_dump()
                }
            )
            return None
    
    def delete(self, mapping_id: int) -> bool:
        """Delete talent equipment mapping.
        
        Args:
            mapping_id: Equipment mapping ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_mapping = self.get_by_id(mapping_id)
            if not db_mapping:
                return False
            
            self.db.delete(db_mapping)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="delete_talent_equipment_mapping",
                table="TalentEquipmentMapping",
                additional_context={"mapping_id": mapping_id}
            )
            return False
    
    def exists_by_talent_and_equipment(self, talent_profile_id: int, equipment_id: int) -> bool:
        """Check if equipment mapping exists for talent and equipment combination.
        
        Args:
            talent_profile_id: Talent profile ID
            equipment_id: Equipment ID
            
        Returns:
            True if mapping exists, False otherwise
        """
        try:
            statement = select(TalentEquipmentMapping).where(
                TalentEquipmentMapping.talent_profile_id == talent_profile_id,
                TalentEquipmentMapping.equipment_id == equipment_id
            )
            return self.db.exec(statement).first() is not None
        except Exception as e:
            log_database_error(
                error=e,
                operation="check_talent_equipment_mapping_exists",
                table="TalentEquipmentMapping",
                additional_context={
                    "talent_profile_id": talent_profile_id,
                    "equipment_id": equipment_id
                }
            )
            return False