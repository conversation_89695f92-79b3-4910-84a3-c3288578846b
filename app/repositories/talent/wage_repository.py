"""Repository for talent wage information management."""

from typing import Annotated, List, Optional

from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentWageInformation, TalentWageHistory
from app.db.session import get_session


class WageRepository:
    """Repository class for wage information database operations."""

    def __init__(self, session: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with a database session.
        
        Args:
            session: SQLModel database session
        """
        self.session = session

    def get_wage_info_by_talent_id(self, talent_id: int) -> Optional[TalentWageInformation]:
        """Get wage information by talent profile ID.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            TalentWageInformation object or None if not found
        """
        statement = select(TalentWageInformation).where(
            TalentWageInformation.talent_profile_id == talent_id
        )
        return self.session.exec(statement).first()

    def get_wage_info_by_id(self, wage_id: int) -> Optional[TalentWageInformation]:
        """Get wage information by ID.
        
        Args:
            wage_id: The wage information ID
            
        Returns:
            TalentWageInformation object or None if not found
        """
        statement = select(TalentWageInformation).where(
            TalentWageInformation.id == wage_id
        )
        return self.session.exec(statement).first()

    def create_wage_info(self, wage_info: TalentWageInformation) -> TalentWageInformation:
        """Create a new wage information record.
        
        Args:
            wage_info: The wage information to create
            
        Returns:
            Created TalentWageInformation object
        """
        self.session.add(wage_info)
        self.session.commit()
        self.session.refresh(wage_info)
        return wage_info

    def update_wage_info(self, wage_info: TalentWageInformation) -> TalentWageInformation:
        """Update an existing wage information record.
        
        Args:
            wage_info: The wage information to update
            
        Returns:
            Updated TalentWageInformation object
        """
        self.session.add(wage_info)
        self.session.commit()
        self.session.refresh(wage_info)
        return wage_info

    def delete_wage_info(self, wage_id: int) -> bool:
        """Delete a wage information record.
        
        Args:
            wage_id: The wage information ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        wage_info = self.get_wage_info_by_id(wage_id)
        if wage_info:
            self.session.delete(wage_info)
            self.session.commit()
            return True
        return False

    def get_wage_history_by_wage_id(self, wage_id: int) -> List[TalentWageHistory]:
        """Get wage history by wage information ID.
        
        Args:
            wage_id: The wage information ID
            
        Returns:
            List of TalentWageHistory objects
        """
        statement = select(TalentWageHistory).where(
            TalentWageHistory.wage_information_id == wage_id
        )
        return list(self.session.exec(statement).all())

    def get_wage_history_by_id(self, history_id: int) -> Optional[TalentWageHistory]:
        """Get wage history by ID.
        
        Args:
            history_id: The wage history ID
            
        Returns:
            TalentWageHistory object or None if not found
        """
        statement = select(TalentWageHistory).where(
            TalentWageHistory.id == history_id
        )
        return self.session.exec(statement).first()

    def create_wage_history(self, wage_history: TalentWageHistory) -> TalentWageHistory:
        """Create a new wage history record.
        
        Args:
            wage_history: The wage history to create
            
        Returns:
            Created TalentWageHistory object
        """
        self.session.add(wage_history)
        self.session.commit()
        self.session.refresh(wage_history)
        return wage_history

    def update_wage_history(self, wage_history: TalentWageHistory) -> TalentWageHistory:
        """Update an existing wage history record.
        
        Args:
            wage_history: The wage history to update
            
        Returns:
            Updated TalentWageHistory object
        """
        self.session.add(wage_history)
        self.session.commit()
        self.session.refresh(wage_history)
        return wage_history

    def delete_wage_history(self, history_id: int) -> bool:
        """Delete a wage history record.
        
        Args:
            history_id: The wage history ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        wage_history = self.get_wage_history_by_id(history_id)
        if wage_history:
            self.session.delete(wage_history)
            self.session.commit()
            return True
        return False
