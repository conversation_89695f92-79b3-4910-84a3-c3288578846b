"""Skill set repository for talent management.

This module contains the repository class for talent skill set data access operations.
"""

from typing import List, Optional
from sqlmodel import Session, select
from fastapi import Depends

from app.db import get_session, TalentSkillSetMapping
from app.schemas.talent.skill_set_schema import TalentSkillSetCreate
from app.core.logs import log_database_error


class TalentSkillSetRepository:
    """Repository class for talent skill set data access operations.
    
    Handles all database operations related to talent skill sets.
    """
    
    def __init__(self, session: Session = Depends(get_session)):
        """Initialize the repository with a database session.
        
        Args:
            session: Database session dependency
        """
        self.session = session
    
    def create(self, skill_set_data: TalentSkillSetCreate) -> Optional[TalentSkillSetMapping]:
        """Create a new talent skill set record.
        
        Args:
            skill_set_data: Skill set data to create
            
        Returns:
            Created skill set record or None if creation fails
        """
        try:
            db_skill_set = TalentSkillSetMapping(**skill_set_data.model_dump())
            self.session.add(db_skill_set)
            self.session.commit()
            self.session.refresh(db_skill_set)
            return db_skill_set
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="create_skill_set",
                table="talent_skill_set_mapping",
                error=e,
                additional_context=skill_set_data.model_dump()
            )
            return None
    
    def get_by_id(self, skill_set_id: int) -> Optional[TalentSkillSetMapping]:
        """Get a talent skill set by ID.
        
        Args:
            skill_set_id: Skill set ID
            
        Returns:
            Skill set record or None if not found
        """
        try:
            statement = select(TalentSkillSetMapping).where(TalentSkillSetMapping.id == skill_set_id)
            return self.session.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="get_skill_set_by_id",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"skill_set_id": skill_set_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> List[TalentSkillSetMapping]:
        """Get all skill sets for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of skill set records for the talent
        """
        try:
            statement = select(TalentSkillSetMapping).where(
                TalentSkillSetMapping.talent_profile_id == talent_profile_id
            )
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_skill_sets_by_talent_id",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return []
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[TalentSkillSetMapping]:
        """Get all talent skill sets with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of skill set records
        """
        try:
            statement = select(TalentSkillSetMapping).offset(skip).limit(limit)
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_all_skill_sets",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, skill_set_id: int, skill_set_data: TalentSkillSetMapping) -> Optional[TalentSkillSetMapping]:
        """Update an existing talent skill set record.
        
        Args:
            skill_set_id: Skill set ID to update
            skill_set_data: Updated skill set data
            
        Returns:
            Updated skill set record or None if update fails
        """
        try:
            db_skill_set = self.get_by_id(skill_set_id)
            if not db_skill_set:
                return None
            
            # Update fields
            for field, value in skill_set_data.model_dump(exclude_unset=True).items():
                if hasattr(db_skill_set, field) and field != "id":
                    setattr(db_skill_set, field, value)
            
            self.session.add(db_skill_set)
            self.session.commit()
            self.session.refresh(db_skill_set)
            return db_skill_set
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="update_skill_set",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"skill_set_id": skill_set_id}
            )
            return None
    
    def delete(self, skill_set_id: int) -> bool:
        """Delete a talent skill set record.
        
        Args:
            skill_set_id: Skill set ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_skill_set = self.get_by_id(skill_set_id)
            if not db_skill_set:
                return False
            
            self.session.delete(db_skill_set)
            self.session.commit()
            return True
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="delete_skill_set",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"skill_set_id": skill_set_id}
            )
            return False
    
    def exists_by_talent_id(self, talent_profile_id: int) -> bool:
        """Check if skill set records exist for a talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            True if skill set records exist, False otherwise
        """
        try:
            statement = select(TalentSkillSetMapping).where(
                TalentSkillSetMapping.talent_profile_id == talent_profile_id
            )
            result = self.session.exec(statement).first()
            return result is not None
        except Exception as e:
            log_database_error(
                operation="check_skill_set_exists_by_talent_id",
                table="talent_skill_set_mapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return False