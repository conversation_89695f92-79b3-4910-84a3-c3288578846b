"""Repository for managing talent chronic conditions data."""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select

from app.db import TalentCronicConditions
from app.db.session import get_session


class TalentChronicConditionsRepository:
    """Repository class for talent chronic conditions CRUD operations."""
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]) -> None:
        """Initialize the repository with database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db

    def create_chronic_condition(
        self, chronic_condition: TalentCronicConditions
    ) -> TalentCronicConditions:
        """Create a new chronic condition record.
        
        Args:
            chronic_condition: The chronic condition data to create
            
        Returns:
            The created chronic condition record
        """
        self.db.add(chronic_condition)
        self.db.commit()
        self.db.refresh(chronic_condition)
        return chronic_condition

    def get_chronic_conditions_by_talent_id(
        self, talent_id: int
    ) -> List[TalentCronicConditions]:
        """Get all chronic conditions for a specific talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of chronic condition records
        """
        statement = select(TalentCronicConditions).where(
            TalentCronicConditions.talent_profile_id == talent_id
        )
        return list(self.db.exec(statement).all())

    def get_chronic_condition_by_id(
        self, condition_id: int
    ) -> Optional[TalentCronicConditions]:
        """Get a specific chronic condition by ID.
        
        Args:
            condition_id: The chronic condition ID
            
        Returns:
            The chronic condition record if found, None otherwise
        """
        statement = select(TalentCronicConditions).where(
            TalentCronicConditions.id == condition_id
        )
        return self.db.exec(statement).first()

    def update_chronic_condition(
        self, chronic_condition: TalentCronicConditions
    ) -> TalentCronicConditions:
        """Update an existing chronic condition record.
        
        Args:
            chronic_condition: The updated chronic condition data
            
        Returns:
            The updated chronic condition record
        """
        self.db.add(chronic_condition)
        self.db.commit()
        self.db.refresh(chronic_condition)
        return chronic_condition

    def delete_chronic_condition(self, condition_id: int) -> bool:
        """Delete a chronic condition record.
        
        Args:
            condition_id: The chronic condition ID to delete
            
        Returns:
            True if deleted successfully, False otherwise
        """
        statement = select(TalentCronicConditions).where(
            TalentCronicConditions.id == condition_id
        )
        condition = self.db.exec(statement).first()
        if condition:
            self.db.delete(condition)
            self.db.commit()
            return True
        return False
