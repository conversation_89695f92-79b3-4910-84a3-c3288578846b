"""Client information repository for talent management.

This module contains the repository class for talent client information CRUD operations.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentClientInfo
from app.db.session import get_session
from app.schemas.talent.client_info_schema import (
    TalentClientInfoCreate
)
from app.core.logs import log_database_error


class TalentClientInfoRepository:
    """Repository class for talent client information operations.
    
    Handles all database operations related to talent client information.
    """
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db
    
    def create(self, client_info_data: TalentClientInfoCreate) -> Optional[TalentClientInfo]:
        """Create a new talent client information record.
        
        Args:
            client_info_data: Client information data to create
            
        Returns:
            Created client information record or None if creation fails
        """
        try:
            db_client_info = TalentClientInfo(**client_info_data.model_dump())
            self.db.add(db_client_info)
            self.db.commit()
            self.db.refresh(db_client_info)
            return db_client_info
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="create_talent_client_info",
                table="TalentClientInfo",
                additional_context=client_info_data.model_dump()
            )
            return None
    
    def get_by_id(self, client_info_id: int) -> Optional[TalentClientInfo]:
        """Get talent client information by ID.
        
        Args:
            client_info_id: Client information ID
            
        Returns:
            Client information record or None if not found
        """
        try:
            statement = select(TalentClientInfo).where(
                TalentClientInfo.id == client_info_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_talent_client_info_by_id",
                table="TalentClientInfo",
                additional_context={"client_info_id": client_info_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> Optional[TalentClientInfo]:
        """Get client information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Client information record or None if not found
        """
        try:
            statement = select(TalentClientInfo).where(
                TalentClientInfo.talent_profile_id == talent_profile_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_talent_client_info_by_talent_id",
                table="TalentClientInfo",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return None
    
    def get_by_client_id(self, client_id: int) -> List[TalentClientInfo]:
        """Get all talent client information for a specific client.
        
        Args:
            client_id: Client ID
            
        Returns:
            List of client information records
        """
        try:
            statement = select(TalentClientInfo).where(
                TalentClientInfo.client_id == client_id
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_talent_client_info_by_client_id",
                table="TalentClientInfo",
                additional_context={"client_id": client_id}
            )
            return []
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[TalentClientInfo]:
        """Get all talent client information with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of client information records
        """
        try:
            statement = select(TalentClientInfo).offset(skip).limit(limit)
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_all_talent_client_info",
                table="TalentClientInfo",
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, client_info_id: int, client_info_data: TalentClientInfo) -> Optional[TalentClientInfo]:
        """Update talent client information.
        
        Args:
            client_info_id: Client information ID
            client_info_data: Updated client information data
            
        Returns:
            Updated client information record or None if update fails
        """
        try:
            db_client_info = self.get_by_id(client_info_id)
            if not db_client_info:
                return None
            
            update_data = client_info_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                if hasattr(db_client_info, field):
                    setattr(db_client_info, field, value)
            
            self.db.add(db_client_info)
            self.db.commit()
            self.db.refresh(db_client_info)
            return db_client_info
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="update_talent_client_info",
                table="TalentClientInfo",
                additional_context={
                    "client_info_id": client_info_id,
                    "update_data": client_info_data.model_dump()
                }
            )
            return None
    
    def delete(self, client_info_id: int) -> bool:
        """Delete talent client information.
        
        Args:
            client_info_id: Client information ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_client_info = self.get_by_id(client_info_id)
            if not db_client_info:
                return False
            
            self.db.delete(db_client_info)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="delete_talent_client_info",
                table="TalentClientInfo",
                additional_context={"client_info_id": client_info_id}
            )
            return False
    
    def exists_by_talent_id(self, talent_profile_id: int) -> bool:
        """Check if client information exists for a talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            True if client information exists, False otherwise
        """
        try:
            statement = select(TalentClientInfo).where(
                TalentClientInfo.talent_profile_id == talent_profile_id
            )
            return self.db.exec(statement).first() is not None
        except Exception as e:
            log_database_error(
                error=e,
                operation="check_talent_client_info_exists",
                table="TalentClientInfo",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            raise e