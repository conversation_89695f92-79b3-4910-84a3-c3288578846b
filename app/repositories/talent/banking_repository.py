"""Banking information repository for talent management.

This module contains the repository class for banking information CRUD operations.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from sqlmodel import Session, select
from app.db.models import TalentBankingInformationMapping
from app.db.session import get_session
from app.schemas.talent.banking_schema import (
    TalentBankingInformationCreate
)
from app.core.logs import log_database_error


class BankingRepository:
    """Repository class for talent banking information operations.
    
    Handles all database operations related to talent banking information.
    """
    
    def __init__(self, db: Annotated[Session, Depends(get_session)]):
        """Initialize the repository with a database session.
        
        Args:
            db: SQLModel database session
        """
        self.db = db
    
    def create(self, banking_data: TalentBankingInformationCreate) -> Optional[TalentBankingInformationMapping]:
        """Create a new banking information record.
        
        Args:
            banking_data: Banking information data to create
            
        Returns:
            Created banking information record or None if creation fails
        """
        try:
            db_banking = TalentBankingInformationMapping(**banking_data.model_dump())
            self.db.add(db_banking)
            self.db.commit()
            self.db.refresh(db_banking)
            return db_banking
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="create_banking_information",
                table="TalentBankingInformationMapping",
                additional_context=banking_data.model_dump()
            )
            return None
    
    def get_by_id(self, banking_id: int) -> Optional[TalentBankingInformationMapping]:
        """Get banking information by ID.
        
        Args:
            banking_id: Banking information ID
            
        Returns:
            Banking information record or None if not found
        """
        try:
            statement = select(TalentBankingInformationMapping).where(
                TalentBankingInformationMapping.id == banking_id
            )
            return self.db.exec(statement).first()
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_banking_information_by_id",
                table="TalentBankingInformationMapping",
                additional_context={"banking_id": banking_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> List[TalentBankingInformationMapping]:
        """Get all banking information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of banking information records
        """
        try:
            statement = select(TalentBankingInformationMapping).where(
                TalentBankingInformationMapping.talent_profile_id == talent_profile_id
            )
            return list(self.db.exec(statement).all())
        except Exception as e:
            log_database_error(
                error=e,
                operation="get_banking_information_by_talent_id",
                table="TalentBankingInformationMapping",
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return []
    
    def update(self, banking_id: int, banking_data: TalentBankingInformationMapping) -> Optional[TalentBankingInformationMapping]:
        """Update banking information.
        
        Args:
            banking_id: Banking information ID
            banking_data: Updated banking information data
            
        Returns:
            Updated banking information record or None if update fails
        """
        try:
            db_banking = self.get_by_id(banking_id)
            if not db_banking:
                return None
            
            update_data = banking_data.model_dump()
            for field, value in update_data.items():
                setattr(db_banking, field, value)
            
            self.db.add(db_banking)
            self.db.commit()
            self.db.refresh(db_banking)
            return db_banking
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="update_banking_information",
                table="TalentBankingInformationMapping",
                additional_context={
                    "banking_id": banking_id,
                    "update_data": banking_data.model_dump()
                }
            )
            return None
    
    def delete(self, banking_id: int) -> bool:
        """Delete banking information.
        
        Args:
            banking_id: Banking information ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_banking = self.get_by_id(banking_id)
            if not db_banking:
                return False
            
            self.db.delete(db_banking)
            self.db.commit()
            return True
        except Exception as e:
            self.db.rollback()
            log_database_error(
                error=e,
                operation="delete_banking_information",
                table="TalentBankingInformationMapping",
                additional_context={"banking_id": banking_id}
            )
            return False
    
    def exists_by_talent_and_account(self, talent_profile_id: int, account_number: str) -> bool:
        """Check if banking information exists for a talent with specific account number.
        
        Args:
            talent_profile_id: Talent profile ID
            account_number: Account number to check
            
        Returns:
            True if banking information exists, False otherwise
        """
        try:
            statement = select(TalentBankingInformationMapping).where(
                TalentBankingInformationMapping.talent_profile_id == talent_profile_id,
                TalentBankingInformationMapping.account_number == account_number
            )
            return self.db.exec(statement).first() is not None
        except Exception as e:
            log_database_error(
                error=e,
                operation="check_banking_information_exists",
                table="TalentBankingInformationMapping",
                additional_context={
                    "talent_profile_id": talent_profile_id,
                    "account_number": account_number
                }
            )
            raise e
