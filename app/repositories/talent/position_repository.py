"""Position repository for talent management.

This module contains the repository class for talent position data access operations."""

from typing import List, Optional
from sqlmodel import Session, select
from fastapi import Depends

from app.db import get_session
from app.db.models import TalentPositionMapping
from app.schemas.talent.position_schema import TalentPositionCreate
from app.core.logs import log_database_error


class TalentPositionRepository:
    """Repository class for talent position data access operations.
    
    Handles all database operations related to talent positions.
    """
    
    def __init__(self, session: Session = Depends(get_session)):
        """Initialize the repository with a database session.
        
        Args:
            session: Database session dependency
        """
        self.session = session
    
    def create(self, position_data: TalentPositionCreate) -> Optional[TalentPositionMapping]:
        """Create a new talent position record.
        
        Args:
            position_data: Position data to create
            
        Returns:
            Created position record or None if creation fails
        """
        try:
            db_position = TalentPositionMapping(**position_data.model_dump())
            self.session.add(db_position)
            self.session.commit()
            self.session.refresh(db_position)
            return db_position
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="create_position",
                table="talent_position_mapping",
                error=e,
                additional_context=position_data.model_dump()
            )
            return None
    
    def get_by_id(self, position_id: int) -> Optional[TalentPositionMapping]:
        """Get a talent position by ID.
        
        Args:
            position_id: Position ID
            
        Returns:
            Position record or None if not found
        """
        try:
            statement = select(TalentPositionMapping).where(TalentPositionMapping.id == position_id)
            return self.session.exec(statement).first()
        except Exception as e:
            log_database_error(
                operation="get_position_by_id",
                table="talent_position_mapping",
                error=e,
                additional_context={"position_id": position_id}
            )
            return None
    
    def get_by_talent_id(self, talent_profile_id: int) -> List[TalentPositionMapping]:
        """Get all positions for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of position records for the talent
        """
        try:
            statement = select(TalentPositionMapping).where(
                TalentPositionMapping.talent_profile_id == talent_profile_id
            )
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_positions_by_talent_id",
                table="talent_position_mapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return []
    
    def get_all(self, skip: int = 0, limit: int = 100) -> List[TalentPositionMapping]:
        """Get all talent positions with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of position records
        """
        try:
            statement = select(TalentPositionMapping).offset(skip).limit(limit)
            return list(self.session.exec(statement).all())
        except Exception as e:
            log_database_error(
                operation="get_all_positions",
                table="talent_position_mapping",
                error=e,
                additional_context={"skip": skip, "limit": limit}
            )
            return []
    
    def update(self, position_id: int, position_data: TalentPositionMapping) -> Optional[TalentPositionMapping]:
        """Update an existing talent position record.
        
        Args:
            position_id: Position ID to update
            position_data: Updated position data
            
        Returns:
            Updated position record or None if update fails
        """
        try:
            db_position = self.get_by_id(position_id)
            if not db_position:
                return None
            
            # Update fields
            for field, value in position_data.model_dump(exclude_unset=True).items():
                if hasattr(db_position, field) and field != "id":
                    setattr(db_position, field, value)
            
            self.session.add(db_position)
            self.session.commit()
            self.session.refresh(db_position)
            return db_position
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="update_position",
                table="talent_position_mapping",
                error=e,
                additional_context={"position_id": position_id}
            )
            return None
    
    def delete(self, position_id: int) -> bool:
        """Delete a talent position record.
        
        Args:
            position_id: Position ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            db_position = self.get_by_id(position_id)
            if not db_position:
                return False
            
            self.session.delete(db_position)
            self.session.commit()
            return True
        except Exception as e:
            self.session.rollback()
            log_database_error(
                operation="delete_position",
                table="talent_position_mapping",
                error=e,
                additional_context={"position_id": position_id}
            )
            return False
    
    def exists_by_talent_id(self, talent_profile_id: int) -> bool:
        """Check if position records exist for a talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            True if position records exist, False otherwise
        """
        try:
            statement = select(TalentPositionMapping).where(
                TalentPositionMapping.talent_profile_id == talent_profile_id
            )
            result = self.session.exec(statement).first()
            return result is not None
        except Exception as e:
            log_database_error(
                operation="check_position_exists_by_talent_id",
                table="talent_position_mapping",
                error=e,
                additional_context={"talent_profile_id": talent_profile_id}
            )
            return False