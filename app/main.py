"""
Main application module for the BPO Admin backend.

This module initializes and configures the FastAPI application, sets up logging,
database connections, and starts the application server.
"""

from contextlib import asynccontextmanager

import uvicorn
from fastapi import FastAPI

from app.api.v1 import auth_router, profile_router, user_router, client_info_router, talent_client_info_router, equipment_router, role_router, module_router, role_module_permission_mapping_router
from app.api.v1.talent import (
    candidate_router,
    chronic_conditions_router,
    documents_router,
    ongoing_health_router,
    past_health_router,
    wage_router,
    banking_router,
    payroll_router,
    emergency_contact_router,
    skill_set_router,
    position_router,
    vacation_router,
    location_router,
    equipment_mapping_router,
    software_mapping_router,
)
from app.core.config import settings
from app.core.logs import logger, configure_fastapi_logging
from app.db.init_db import init_db
from app.db.seeders.module_seeder import seed_modules
from app.db.seeders.user_seeder import seed_users
from app.middlewares.cors_middleware import cors_middleware


@asynccontextmanager
async def lifespan(_: FastAPI):
    """
    Summary:
        Lifespan context manager for the FastAPI application.

        This async context manager handles application startup and shutdown events.
        It is called when the FastAPI application starts up and shuts down.

    Args:
        _ (FastAPI): The FastAPI application instance (unused parameter)

    Returns:
        None: This function doesn't return any value directly, but yields control
        back to the application during its lifetime.

    Raises:
        Exception: If database initialization fails during startup
    """
    logger.info("Starting application initialization")
    try:
        # Initialize the database
        init_db()
        # Run the database seeder
        logger.info("Running database seeder")
        if bool(settings.run_seeder):
            seed_users()
            seed_modules()
        else:
            logger.info("Seeder is not enabled")

        logger.info("Database initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize database: {str(e)}")
        raise
    yield
    logger.info("Application shutdown complete")


app = FastAPI(
    lifespan=lifespan,
    root_path="/api/v1",
    title="BPO Admin",
    version="1.0.0",
)

cors_middleware(app)

app.include_router(router=auth_router, prefix="/auth", tags=["Auth"])
app.include_router(router=profile_router, prefix="/profile", tags=["Profile"])
app.include_router(router=user_router, prefix="/users", tags=["User Management"])
app.include_router(router=candidate_router, prefix="/talent", tags=["Talent Profiles"])
app.include_router(router=chronic_conditions_router, prefix="/talent", tags=["Talent Chronic Conditions"])
app.include_router(router=documents_router, prefix="/talent", tags=["Talent Documents"])
app.include_router(router=ongoing_health_router, prefix="/talent", tags=["Talent Ongoing Health"])
app.include_router(router=past_health_router, prefix="/talent", tags=["Talent Past Health"])
app.include_router(router=wage_router, prefix="/talent", tags=["Talent Wage Information"])
app.include_router(router=banking_router, prefix="/talent", tags=["Talent Banking Information"])
app.include_router(router=payroll_router, prefix="/talent", tags=["Talent Payroll Information"])
app.include_router(router=emergency_contact_router, prefix="/talent", tags=["Talent Emergency Contact"])
app.include_router(router=talent_client_info_router, prefix="/talent", tags=["Talent Client Information"])
app.include_router(router=skill_set_router, prefix="/talent", tags=["Talent Skill Sets"])
app.include_router(router=position_router, prefix="/talent", tags=["Talent Positions"])
app.include_router(router=vacation_router, prefix="/talent", tags=["Talent Vacation Mapping"])
app.include_router(router=location_router, prefix="/talent", tags=["Talent Location Mapping"])
app.include_router(router=equipment_mapping_router, prefix="/talent", tags=["Talent Equipment Mapping"])
app.include_router(router=software_mapping_router, prefix="/talent", tags=["Talent Software Mapping"])
app.include_router(router=client_info_router, prefix="/master/clients", tags=["Master Client Information"])
app.include_router(router=equipment_router, prefix="/master", tags=["Master Equipment"])
app.include_router(router=role_router, prefix="/master", tags=["Master Role"])
app.include_router(router=module_router, prefix="/master", tags=["Master Module"])
app.include_router(router=role_module_permission_mapping_router, prefix="/master", tags=["Role Module Permission Mapping"])


def start():
    """
    Start the FastAPI application server.

    This function configures the FastAPI logging integration and starts
    the Uvicorn server to serve the application on the configured port.
    """

    configure_fastapi_logging()
    logger.info(f"Starting server on port {settings.app_port}")
    uvicorn.run(
        "app.main:app", host="0.0.0.0", port=int(settings.app_port), reload=True
    )


if __name__ == "__main__":
    start()
