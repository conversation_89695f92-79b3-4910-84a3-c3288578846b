"""Logging configuration module for the application.

This module sets up logging using the loguru library, configuring two log handlers:
- Console logging for all log levels (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- Error log file for ERROR and CRITICAL levels only

It also provides utility functions for logging with context and specific error types.
"""

import sys
import traceback
import inspect
from pathlib import Path
from typing import Any, Dict, Optional

import logging
from loguru import logger

# Remove default handler
logger.remove()

# Create logs directory if it doesn't exist
logs_dir = Path("logs")
logs_dir.mkdir(exist_ok=True)

# Console handler for all log levels
logger.add(
    sys.stderr,
    format=(
        "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | "
        "<level>{level: <8}</level> | "
        "<cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> - "
        "<level>{message}</level>"
    ),
    level="DEBUG",  # Changed to DEBUG to capture all levels in console
    colorize=True,
    backtrace=True,
    diagnose=True,
)

# Error log file - captures only ERROR and CRITICAL levels
logger.add(
    logs_dir / "error.log",
    format=(
        "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | "
        "{name}:{function}:{line} | {extra} | {message}"
    ),
    level="ERROR",
    rotation="10 MB",
    retention="30 days",
    compression="zip",
    backtrace=True,
    diagnose=True,
    enqueue=True,  # Thread-safe logging
)


def get_logger(name: Optional[str] = None):
    """
    Get a logger instance with optional name.

    Args:
        name: Optional name for the logger context

    Returns:
        Configured logger instance
    """
    if name:
        return logger.bind(logger_name=name)
    return logger


def log_error_with_context(
    error: Exception, context: Dict[str, Any] | None = None
) -> None:
    """
    Log an error with additional context information including file location and stack trace.

    Args:
        error: The exception to log
        context: Additional context information
    """
    context = context or {}
    
    # Get caller information
    frame = inspect.currentframe()
    if frame and frame.f_back:
        caller_frame = frame.f_back
        caller_file = caller_frame.f_code.co_filename
        caller_function = caller_frame.f_code.co_name
        caller_line = caller_frame.f_lineno
        
        # Add caller information to context
        context.update({
            "caller_file": caller_file,
            "caller_function": caller_function,
            "caller_line": caller_line
        })
    
    # Get full traceback
    tb_str = traceback.format_exc()
    
    # Log with enhanced context
    logger.bind(**context).error(
        f"Error occurred: {str(error)}\n\nFull traceback:\n{tb_str}"
    )


def log_api_error(
    endpoint: str,
    method: str,
    status_code: int,
    error: str,
    user_id: Optional[str] = None,
) -> None:
    """
    Log API-specific errors with structured context including file location and stack trace.

    Args:
        endpoint: API endpoint where error occurred
        method: HTTP method
        status_code: HTTP status code
        error: Error message
        user_id: Optional[str] = None,
    """
    # Get caller information
    frame = inspect.currentframe()
    if frame and frame.f_back:
        caller_frame = frame.f_back
        caller_file = caller_frame.f_code.co_filename
        caller_function = caller_frame.f_code.co_name
        caller_line = caller_frame.f_lineno
    else:
        caller_file = "unknown"
        caller_function = "unknown"
        caller_line = 0
    
    context: dict[str, Any] = {
        "endpoint": endpoint,
        "method": method,
        "status_code": status_code,
        "user_id": user_id,
        "caller_file": caller_file,
        "caller_function": caller_function,
        "caller_line": caller_line
    }
    
    # Get current traceback if available
    tb_str = traceback.format_stack()
    stack_trace = ''.join(tb_str[-5:])  # Last 5 frames
    
    logger.bind(**context).error(
        f"API Error: {error}\n\nCall stack:\n{stack_trace}"
    )


def log_database_error(
    operation: str, table: str, error: Exception, additional_context: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log database-specific errors with context including file location and stack trace.

    Args:
        operation: Database operation (SELECT, INSERT, UPDATE, DELETE)
        table: Table name
        error: The database exception
        context: Optional context information
    """
    # Get caller information
    frame = inspect.currentframe()
    if frame and frame.f_back:
        caller_frame = frame.f_back
        caller_file = caller_frame.f_code.co_filename
        caller_function = caller_frame.f_code.co_name
        caller_line = caller_frame.f_lineno
    else:
        caller_file = "unknown"
        caller_function = "unknown"
        caller_line = 0
    
    context: dict[str, Any] = {
        "operation": operation,
        "table": table,
        "context": additional_context,
        "caller_file": caller_file,
        "caller_function": caller_function,
        "caller_line": caller_line
    }
    
    # Get full traceback
    tb_str = traceback.format_exc()
    
    logger.bind(**context).error(
        f"Database Error: {str(error)}\n\nFull traceback:\n{tb_str}"
    )


# Configure logger for FastAPI
def configure_fastapi_logging():
    """
    Configure logging specifically for FastAPI applications.
    """

    # Intercept standard logging and redirect to loguru
    class InterceptHandler(logging.Handler):  # pylint: disable=no-member
        """
        Custom logging handler that intercepts standard library logging
        and redirects log records to Loguru.

        This handler allows seamless integration between Python's built-in
        logging system and Loguru by capturing log records from the standard
        library and passing them to Loguru's logging system.
        """

        def emit(self, record: logging.LogRecord) -> None:
            # Get corresponding Loguru level if it exists
            try:
                level = logger.level(record.levelname).name
            except ValueError:
                level = record.levelno

            # Find caller from where originated the logged message
            frame, depth = logging.currentframe(), 2  # pylint: disable=no-member
            while frame and frame.f_code.co_filename == logging.__file__:
                frame = frame.f_back
                depth += 1

            logger.opt(depth=depth, exception=record.exc_info).log(
                level, record.getMessage()
            )

    # Replace handlers for specific loggers
    logging.getLogger("uvicorn").handlers = [
        InterceptHandler()
    ]  # pylint: disable=no-member
    logging.getLogger("uvicorn.access").handlers = [
        InterceptHandler()
    ]  # pylint: disable=no-member
    logging.getLogger("fastapi").handlers = [
        InterceptHandler()
    ]  # pylint: disable=no-member

    # Set levels
    logging.getLogger("uvicorn").setLevel(logging.INFO)  # pylint: disable=no-member
    logging.getLogger("uvicorn.access").setLevel(
        logging.INFO
    )  # pylint: disable=no-member
    logging.getLogger("fastapi").setLevel(logging.INFO)  # pylint: disable=no-member


# Export the main logger instance
__all__ = [
    "logger",
    "get_logger",
    "log_error_with_context",
    "log_api_error",
    "log_database_error",
    "configure_fastapi_logging",
]
