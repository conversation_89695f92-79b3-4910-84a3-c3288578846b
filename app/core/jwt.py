from datetime import timedelta, datetime, timezone
from typing import Annotated, Any

import jwt
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2PasswordBearer
from jwt import InvalidTokenError
from sqlmodel import or_, select

from app.db.models import User
from app.db.session import get_session
from app.core.config import settings
from app.schemas.token_data_schema import TokenData

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="auth/login-token")


async def create_access_token(
    data: dict[str, Any], expires_delta: timedelta | None = None
):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(
            minutes=int(settings.jwt_token_expiration)
        )
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(
        to_encode, settings.jwt_secret, algorithm=settings.jwt_algo
    )
    return encoded_jwt


async def get_user(email: str | None) -> User | None:
    session = next(get_session())
    statement = select(User).where(or_(User.email == email))
    user = session.exec(statement).first()
    return user


async def get_current_user(
    token: Annotated[str, Depends(oauth2_scheme)],
) -> User | None:
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, settings.jwt_secret, algorithms=[settings.jwt_algo])
        email: str = payload.get("sub")
        if email is None:
            raise credentials_exception
        token_data = TokenData(email=email)
    except InvalidTokenError:
        raise credentials_exception
    user = await get_user(email=token_data.email)
    if user is None:
        print(credentials_exception)
        raise credentials_exception
    return user


def get_current_active_user(current_user: Annotated[User, Depends(get_current_user)]):
    if not current_user.is_active:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user
