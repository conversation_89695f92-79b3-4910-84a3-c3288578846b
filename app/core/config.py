from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", extra="ignore")
    database_url: str = ""
    app_port: str = ""
    crypto_key: str = ""
    env_type: str = ""
    run_seeder: int = 1
    jwt_secret: str = ""
    jwt_algo: str = "HS256"
    jwt_token_expiration: int = 525960


settings = Settings()
