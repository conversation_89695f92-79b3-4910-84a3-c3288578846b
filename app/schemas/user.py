"""User schemas for request/response validation."""

from typing import Optional
from pydantic import EmailStr
from app.schemas.base import CamelCaseModel


class UserCreate(CamelCaseModel):
    """Schema for creating a new user."""

    name: str
    email: EmailStr
    phone: str
    password: str


class UserUpdate(CamelCaseModel):
    """Schema for updating user data."""

    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    pic: Optional[str] = None


class UserResponse(CamelCaseModel):
    """Schema for user response data."""

    id: int
    name: str
    email: str
    phone: str
    pic: Optional[str] = None
    is_active: bool
    is_superuser: bool
