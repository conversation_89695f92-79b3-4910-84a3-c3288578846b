"""Schemas package for request/response validation."""

from .user import User<PERSON><PERSON>, UserResponse
from .login_schema import LoginSchema
from .token_data_schema import TokenData
from .talent.chronic_conditions_schema import (
    ChronicConditionCreate,
    ChronicConditionUpdate,
    ChronicConditionResponse,
)
from .talent.ongoing_health_schema import (
    OngoingHealthIssueCreate,
    OngoingHealthIssueUpdate,
    OngoingHealthIssueResponse,
)
from .talent.past_health_schema import (
    PastHealthIssueCreate,
    PastHealthIssueUpdate,
    PastHealthIssueResponse,
)
from .talent.talent_profile_schema import (
    ProfileCreate,
    ProfileUpdate,
    ProfileResponse,
)
from .talent.wage_schema import (
    WageInformationCreate,
    WageInformationUpdate,
    WageInformationResponse,
    WageHistoryCreate,
    WageHistoryUpdate,
    WageHistoryResponse,
)

__all__ = [
    "UserCreate",
    "UserResponse",
    "LoginSchema",
    "TokenData",
    "ChronicConditionCreate",
    "ChronicConditionUpdate",
    "ChronicConditionResponse",
    "OngoingHealthIssueCreate",
    "OngoingHealthIssueUpdate",
    "OngoingHealthIssueResponse",
    "PastHealthIssueCreate",
    "PastHealthIssueUpdate",
    "PastHealthIssueResponse",
    "ProfileCreate",
    "ProfileUpdate",
    "ProfileResponse",
    "WageInformationCreate",
    "WageInformationUpdate",
    "WageInformationResponse",
    "WageHistoryCreate",
    "WageHistoryUpdate",
    "WageHistoryResponse",
]
