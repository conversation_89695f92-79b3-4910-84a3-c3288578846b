"""Schemas for master role management.

This module contains Pydantic schemas for master role CRUD operations.
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class MasterRoleCreate(BaseModel):
    """Schema for creating a new master role record."""
    
    name: str = Field(..., max_length=20, description="Role name")


class MasterRoleUpdate(BaseModel):
    """Schema for updating an existing master role record."""
    
    name: Optional[str] = Field(None, max_length=20, description="Role name")


class MasterRoleResponse(BaseModel):
    """Schema for master role API responses."""
    
    id: int = Field(..., description="Role ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    name: str = Field(..., description="Role name")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True