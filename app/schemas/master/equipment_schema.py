"""Schemas for master equipment management.

This module contains Pydantic schemas for master equipment CRUD operations.
"""

from typing import Optional
from pydantic import BaseModel, Field
from datetime import datetime


class MasterEquipmentCreate(BaseModel):
    """Schema for creating a new master equipment record."""
    
    name: str = Field(..., max_length=200, description="Equipment name")
    serial_number: Optional[str] = Field(None, max_length=200, description="Equipment serial number")
    model: Optional[str] = Field(None, max_length=200, description="Equipment model")
    manufacturer: Optional[str] = Field(None, max_length=200, description="Equipment manufacturer")
    model_number: Optional[str] = Field(None, max_length=200, description="Equipment model number")
    desktop_name: Optional[str] = Field(None, max_length=200, description="Desktop name")
    anydesk_id: Optional[str] = Field(None, max_length=200, description="AnyDesk ID")


class MasterEquipmentUpdate(BaseModel):
    """Schema for updating an existing master equipment record."""
    
    name: Optional[str] = Field(None, max_length=200, description="Equipment name")
    serial_number: Optional[str] = Field(None, max_length=200, description="Equipment serial number")
    model: Optional[str] = Field(None, max_length=200, description="Equipment model")
    manufacturer: Optional[str] = Field(None, max_length=200, description="Equipment manufacturer")
    model_number: Optional[str] = Field(None, max_length=200, description="Equipment model number")
    desktop_name: Optional[str] = Field(None, max_length=200, description="Desktop name")
    anydesk_id: Optional[str] = Field(None, max_length=200, description="AnyDesk ID")


class MasterEquipmentResponse(BaseModel):
    """Schema for master equipment API responses."""
    
    id: int = Field(..., description="Equipment ID")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    name: str = Field(..., description="Equipment name")
    serial_number: Optional[str] = Field(None, description="Equipment serial number")
    model: Optional[str] = Field(None, description="Equipment model")
    manufacturer: Optional[str] = Field(None, description="Equipment manufacturer")
    model_number: Optional[str] = Field(None, description="Equipment model number")
    desktop_name: Optional[str] = Field(None, description="Desktop name")
    anydesk_id: Optional[str] = Field(None, description="AnyDesk ID")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True