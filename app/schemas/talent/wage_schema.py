"""Schemas for talent wage information validation."""

from datetime import datetime
from typing import Optional

from pydantic import Field
from app.schemas.base import CamelCaseModel


class WageInformationCreate(CamelCaseModel):
    """Schema for creating talent wage information."""
    
    talent_profile_id: int = Field(..., description="Talent profile ID")
    currency: Optional[str] = Field(None, description="Currency type")
    weekly_base_wage: Optional[float] = Field(None, description="Weekly base wage amount")
    work_days: Optional[str] = Field(None, description="Work days schedule")
    schedule: Optional[str] = Field(None, description="Work schedule details")
    timezone: Optional[str] = Field("PST", description="Timezone")
    type_of_employee: Optional[str] = Field(None, description="Type of employee")


class WageInformationUpdate(CamelCaseModel):
    """Schema for updating talent wage information."""
    
    currency: Optional[str] = Field(None, description="Currency type")
    weekly_base_wage: Optional[float] = Field(None, description="Weekly base wage amount")
    work_days: Optional[str] = Field(None, description="Work days schedule")
    schedule: Optional[str] = Field(None, description="Work schedule details")
    timezone: Optional[str] = Field(None, description="Timezone")
    type_of_employee: Optional[str] = Field(None, description="Type of employee")


class WageInformationResponse(CamelCaseModel):
    """Schema for wage information response."""
    
    id: int = Field(..., description="Wage information ID")
    talent_profile_id: int = Field(..., description="Talent profile ID")
    currency: Optional[str] = Field(None, description="Currency type")
    weekly_base_wage: Optional[float] = Field(None, description="Weekly base wage amount")
    work_days: Optional[str] = Field(None, description="Work days schedule")
    schedule: Optional[str] = Field(None, description="Work schedule details")
    timezone: Optional[str] = Field(None, description="Timezone")
    type_of_employee: Optional[str] = Field(None, description="Type of employee")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")




class WageHistoryCreate(CamelCaseModel):
    """Schema for creating talent wage history."""
    
    wage_information_id: int = Field(..., description="Wage information ID")
    period: str = Field(..., description="Period of the wage history")
    start_date: datetime = Field(..., description="Start date of the period")
    end_date: datetime = Field(..., description="End date of the period")
    amount: float = Field(..., description="Wage amount for the period")


class WageHistoryUpdate(CamelCaseModel):
    """Schema for updating wage history."""
    
    period: Optional[str] = Field(None, description="Period of the wage history")
    start_date: Optional[datetime] = Field(None, description="Start date of the period")
    end_date: Optional[datetime] = Field(None, description="End date of the period")
    amount: Optional[float] = Field(None, description="Wage amount for the period")


class WageHistoryResponse(CamelCaseModel):
    """Schema for wage history response."""
    
    id: int = Field(..., description="Wage history ID")
    wage_information_id: int = Field(..., description="Wage information ID")
    period: str = Field(..., description="Period of the wage history")
    start_date: datetime = Field(..., description="Start date of the period")
    end_date: datetime = Field(..., description="End date of the period")
    amount: float = Field(..., description="Wage amount for the period")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
