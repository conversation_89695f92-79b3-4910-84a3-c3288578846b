"""Payroll information schema definitions for talent management.

This module contains Pydantic models for payroll information validation,
including create, update, and response schemas.
"""

from typing import Optional
from pydantic import Field
from datetime import datetime
from app.schemas.base import CamelCaseModel


class TalentPayrollInformationCreate(CamelCaseModel):
    """Schema for creating new talent payroll information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        imms: IMMS number of the talent
        curp: CURP of the talent
        rfc: RFC of the talent
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    imms: Optional[str] = Field(None, max_length=100, description="IMMS number")
    curp: Optional[str] = Field(None, max_length=100, description="CURP number")
    rfc: Optional[str] = Field(None, max_length=100, description="RFC number")


class TalentPayrollInformationUpdate(CamelCaseModel):
    """Schema for updating existing talent payroll information.
    
    All fields are optional to allow partial updates.
    """
    
    imms: Optional[str] = Field(None, max_length=100, description="IMMS number")
    curp: Optional[str] = Field(None, max_length=100, description="CURP number")
    rfc: Optional[str] = Field(None, max_length=100, description="RFC number")


class TalentPayrollInformationResponse(CamelCaseModel):
    """Schema for talent payroll information API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    imms: Optional[str] = Field(None, description="IMMS number")
    curp: Optional[str] = Field(None, description="CURP number")
    rfc: Optional[str] = Field(None, description="RFC number")