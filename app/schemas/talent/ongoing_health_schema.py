"""Schemas for talent ongoing health issues API."""

from datetime import datetime
from typing import Optional

from pydantic import Field
from app.schemas.base import CamelCaseModel


class OngoingHealthIssueBase(CamelCaseModel):
    """Base schema for ongoing health issues."""
    
    talent_profile_id: int = Field(..., description="ID of the talent profile")
    condition_name: str = Field(..., min_length=1, max_length=255, description="Name of the ongoing health condition")
    description: Optional[str] = Field(None, max_length=1000, description="Detailed description of the condition")
    severity: Optional[str] = Field(None, max_length=50, description="Severity level of the condition")
    treatment_plan: Optional[str] = Field(None, max_length=1000, description="Current treatment plan")
    medication: Optional[str] = Field(None, max_length=500, description="Current medications")
    monitoring_frequency: Optional[str] = Field(None, max_length=100, description="How often the condition is monitored")
    is_active: bool = Field(True, description="Whether the condition is currently active")


class OngoingHealthIssueCreate(OngoingHealthIssueBase):
    """Schema for creating a new ongoing health issue."""
    pass


class OngoingHealthIssueUpdate(CamelCaseModel):
    """Schema for updating an existing ongoing health issue."""
    
    condition_name: Optional[str] = Field(None, min_length=1, max_length=255, description="Name of the ongoing health condition")
    description: Optional[str] = Field(None, max_length=1000, description="Detailed description of the condition")
    severity: Optional[str] = Field(None, max_length=50, description="Severity level of the condition")
    treatment_plan: Optional[str] = Field(None, max_length=1000, description="Current treatment plan")
    medication: Optional[str] = Field(None, max_length=500, description="Current medications")
    monitoring_frequency: Optional[str] = Field(None, max_length=100, description="How often the condition is monitored")
    is_active: Optional[bool] = Field(None, description="Whether the condition is currently active")


class OngoingHealthIssueResponse(OngoingHealthIssueBase):
    """Schema for ongoing health issue responses."""
    
    id: int = Field(..., description="Unique identifier for the ongoing health issue")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the record was last updated")