"""Schemas for talent profile API."""

from datetime import datetime
from typing import Optional

from pydantic import Field
from app.schemas.base import CamelCaseModel


class ProfileBase(CamelCaseModel):
    """Base schema for profiles."""
    
    first_name: str = Field(..., min_length=1, max_length=100, description="First name of the profile")
    last_name: str = Field(..., min_length=1, max_length=100, description="Last name of the profile")
    email: str = Field(..., description="Email address of the profile")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number of the profile")
    date_of_birth: Optional[datetime] = Field(None, description="Date of birth")
    gender: Optional[str] = Field(None, max_length=10, description="Gender")
    nationality: Optional[str] = Field(None, max_length=50, description="Nationality")
    address: Optional[str] = Field(None, max_length=500, description="Address")
    city: Optional[str] = Field(None, max_length=100, description="City")
    state: Optional[str] = Field(None, max_length=100, description="State")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    postal_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    emergency_contact_name: Optional[str] = Field(None, max_length=200, description="Emergency contact name")
    emergency_contact_phone: Optional[str] = Field(None, max_length=20, description="Emergency contact phone")
    emergency_contact_relationship: Optional[str] = Field(None, max_length=50, description="Emergency contact relationship")
    is_active: bool = Field(True, description="Whether the talent profile is active")


class ProfileCreate(ProfileBase):
    """Schema for creating a new talent profile."""
    pass


class ProfileUpdate(CamelCaseModel):
    """Schema for updating an existing profile."""
    
    first_name: Optional[str] = Field(None, min_length=1, max_length=100, description="First name of the profile")
    last_name: Optional[str] = Field(None, min_length=1, max_length=100, description="Last name of the profile")
    email: Optional[str] = Field(None, description="Email address of the profile")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number of the profile")
    date_of_birth: Optional[datetime] = Field(None, description="Date of birth")
    gender: Optional[str] = Field(None, max_length=10, description="Gender")
    nationality: Optional[str] = Field(None, max_length=50, description="Nationality")
    address: Optional[str] = Field(None, max_length=500, description="Address")
    city: Optional[str] = Field(None, max_length=100, description="City")
    state: Optional[str] = Field(None, max_length=100, description="State")
    country: Optional[str] = Field(None, max_length=100, description="Country")
    postal_code: Optional[str] = Field(None, max_length=20, description="Postal code")
    emergency_contact_name: Optional[str] = Field(None, max_length=200, description="Emergency contact name")
    emergency_contact_phone: Optional[str] = Field(None, max_length=20, description="Emergency contact phone")
    emergency_contact_relationship: Optional[str] = Field(None, max_length=50, description="Emergency contact relationship")
    is_active: Optional[bool] = Field(None, description="Whether the talent profile is active")


class ProfileResponse(ProfileBase):
    """Schema for profile responses."""
    
    id: int = Field(..., description="Unique identifier for the profile")
    created_at: datetime = Field(..., description="Timestamp when the record was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when the record was last updated")
