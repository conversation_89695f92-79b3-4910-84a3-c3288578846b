"""Schemas for talent equipment mapping management.

This module contains Pydantic schemas for talent equipment mapping CRUD operations.
"""

from typing import Optional
from datetime import datetime
from pydantic import Field
from app.schemas.base import CamelCaseModel


class TalentEquipmentMappingCreate(CamelCaseModel):
    """Schema for creating new talent equipment mapping.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        equipment_id: Foreign key reference to the associated equipment
        duration_of_use: Duration of equipment usage
        is_replaced: Whether the equipment has been replaced
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    equipment_id: int = Field(..., description="Foreign key reference to equipment")
    duration_of_use: str = Field(..., max_length=20, description="Duration of equipment usage")
    is_replaced: bool = Field(default=False, description="Whether the equipment has been replaced")


class TalentEquipmentMappingUpdate(CamelCaseModel):
    """Schema for updating existing talent equipment mapping.
    
    All fields are optional to allow partial updates.
    """
    
    equipment_id: Optional[int] = Field(default=None, description="Foreign key reference to equipment")
    duration_of_use: Optional[str] = Field(default=None, max_length=20, description="Duration of equipment usage")
    is_replaced: Optional[bool] = Field(default=None, description="Whether the equipment has been replaced")


class TalentEquipmentMappingResponse(CamelCaseModel):
    """Schema for talent equipment mapping API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    equipment_id: int = Field(..., description="Foreign key reference to equipment")
    duration_of_use: str = Field(..., description="Duration of equipment usage")
    is_replaced: bool = Field(..., description="Whether the equipment has been replaced")