"""Schemas for talent software mapping management.

This module contains Pydantic schemas for talent software mapping CRUD operations.
"""

from typing import Optional
from datetime import datetime
from pydantic import Field
from app.schemas.base import CamelCaseModel


class TalentSoftwareMappingCreate(CamelCaseModel):
    """Schema for creating new talent software mapping.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        software: Software name
        software_version: Software version
        software_key: Software key
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    software: str = Field(..., max_length=20, description="Software name")
    software_version: Optional[str] = Field(None, max_length=20, description="Software version")
    software_key: Optional[str] = Field(None, max_length=20, description="Software key")


class TalentSoftwareMappingUpdate(CamelCaseModel):
    """Schema for updating existing talent software mapping.
    
    All fields are optional to allow partial updates.
    """
    
    software: Optional[str] = Field(default=None, max_length=20, description="Software name")
    software_version: Optional[str] = Field(default=None, max_length=20, description="Software version")
    software_key: Optional[str] = Field(default=None, max_length=20, description="Software key")


class TalentSoftwareMappingResponse(CamelCaseModel):
    """Schema for talent software mapping API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    software: str = Field(..., description="Software name")
    software_version: Optional[str] = Field(None, description="Software version")
    software_key: Optional[str] = Field(None, description="Software key")