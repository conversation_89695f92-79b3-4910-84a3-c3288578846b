"""Schemas for talent past health issues API."""

from typing import Optional
from datetime import date
from pydantic import Field
from app.schemas.base import CamelCaseModel


class PastHealthIssueBase(CamelCaseModel):
    """Base schema for past health issue data."""
    
    talent_profile_id: int = Field(..., description="The talent profile ID")
    condition_name: str = Field(..., min_length=1, max_length=255, description="Name of the past health condition")
    description: Optional[str] = Field(None, max_length=1000, description="Description of the condition")
    diagnosis_date: Optional[date] = Field(None, description="Date when the condition was diagnosed")
    treatment_received: Optional[str] = Field(None, max_length=500, description="Treatment received for the condition")
    recovery_status: Optional[str] = Field(None, max_length=100, description="Current recovery status")
    is_resolved: bool = Field(True, description="Whether the condition has been resolved")


class PastHealthIssueCreate(PastHealthIssueBase):
    """Schema for creating a new past health issue."""
    pass


class PastHealthIssueUpdate(CamelCaseModel):
    """Schema for updating an existing past health issue."""
    
    condition_name: Optional[str] = Field(None, min_length=1, max_length=255, description="Name of the past health condition")
    description: Optional[str] = Field(None, max_length=1000, description="Description of the condition")
    diagnosis_date: Optional[date] = Field(None, description="Date when the condition was diagnosed")
    treatment_received: Optional[str] = Field(None, max_length=500, description="Treatment received for the condition")
    recovery_status: Optional[str] = Field(None, max_length=100, description="Current recovery status")
    is_resolved: Optional[bool] = Field(None, description="Whether the condition has been resolved")


class PastHealthIssueResponse(PastHealthIssueBase):
    """Schema for past health issue response data."""
    
    id: int = Field(..., description="Unique identifier for the past health issue")