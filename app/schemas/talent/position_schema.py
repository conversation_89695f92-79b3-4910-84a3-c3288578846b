"""Position schema for talent management.

This module contains Pydantic models for talent position request/response validation.
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class TalentPositionCreate(BaseModel):
    """Schema for creating a new talent position.
    
    Attributes:
        talent_profile_id: ID of the talent profile
        position: Position of the talent
        contract_period: Contract period of the talent (Optional)
        contract_end_date: Contract end date of the talent (Optional)
        role: Role of the talent
        title: Title of the talent position
        client_placed: Client placed of the talent
        time: Working hours of the talent
        start_date: Start date of the talent position
        end_date: End date of the talent position
        termination_date: Termination date of the talent
        reason_for_termination: Reason for termination of the talent
        notes_for_termination: Notes for termination of the talent
        salary: Salary of the talent (Optional)
        hours: Working hours of the talent (Optional)
        hourly_rate: Hourly rate of the talent (Optional)
    """
    
    talent_profile_id: int = Field(..., description="ID of the talent profile")
    position: str = Field(..., max_length=20, description="Position of the talent")
    contract_period: Optional[str] = Field(None, max_length=20, description="Contract period of the talent")
    contract_end_date: Optional[datetime] = Field(None, description="Contract end date of the talent")
    role: str = Field(..., max_length=20, description="Role of the talent")
    title: str = Field(..., max_length=20, description="Title of the talent position")
    client_placed: int = Field(..., description="Client placed of the talent")
    time: Optional[str] = Field(None, max_length=20, description="Working hours of the talent")
    start_date: Optional[datetime] = Field(None, description="Start date of the talent position")
    end_date: Optional[datetime] = Field(None, description="End date of the talent position")
    termination_date: Optional[datetime] = Field(None, description="Termination date of the talent")
    reason_for_termination: str = Field(..., max_length=20, description="Reason for termination of the talent")
    notes_for_termination: str = Field(..., max_length=20, description="Notes for termination of the talent")
    salary: str = Field(..., max_length=20, description="Salary of the talent")
    hours: str = Field(..., max_length=20, description="Working hours of the talent")
    hourly_rate: str = Field(..., max_length=20, description="Hourly rate of the talent")


class TalentPositionUpdate(BaseModel):
    """Schema for updating an existing talent position.
    
    All fields are optional for partial updates.
    """
    
    position: Optional[str] = Field(None, max_length=20, description="Position of the talent")
    contract_period: Optional[str] = Field(None, max_length=20, description="Contract period of the talent")
    contract_end_date: Optional[datetime] = Field(None, description="Contract end date of the talent")
    role: Optional[str] = Field(None, max_length=20, description="Role of the talent")
    title: Optional[str] = Field(None, max_length=20, description="Title of the talent position")
    client_placed: Optional[int] = Field(None, description="Client placed of the talent")
    time: Optional[str] = Field(None, max_length=20, description="Working hours of the talent")
    start_date: Optional[datetime] = Field(None, description="Start date of the talent position")
    end_date: Optional[datetime] = Field(None, description="End date of the talent position")
    termination_date: Optional[datetime] = Field(None, description="Termination date of the talent")
    reason_for_termination: Optional[str] = Field(None, max_length=20, description="Reason for termination of the talent")
    notes_for_termination: Optional[str] = Field(None, max_length=20, description="Notes for termination of the talent")
    salary: Optional[str] = Field(None, max_length=20, description="Salary of the talent")
    hours: Optional[str] = Field(None, max_length=20, description="Working hours of the talent")
    hourly_rate: Optional[str] = Field(None, max_length=20, description="Hourly rate of the talent")


class TalentPositionResponse(BaseModel):
    """Schema for talent position response.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Unique identifier for the talent position")
    created_at: Optional[datetime] = Field(None, description="Timestamp when position was created")
    updated_at: Optional[datetime] = Field(None, description="Timestamp when position was last updated")
    talent_profile_id: int = Field(..., description="ID of the talent profile")
    position: str = Field(..., description="Position of the talent")
    contract_period: Optional[str] = Field(None, description="Contract period of the talent")
    contract_end_date: Optional[datetime] = Field(None, description="Contract end date of the talent")
    role: str = Field(..., description="Role of the talent")
    title: str = Field(..., description="Title of the talent position")
    client_placed: int = Field(..., description="Client placed of the talent")
    time: Optional[str] = Field(None, description="Working hours of the talent")
    start_date: Optional[datetime] = Field(None, description="Start date of the talent position")
    end_date: Optional[datetime] = Field(None, description="End date of the talent position")
    termination_date: Optional[datetime] = Field(None, description="Termination date of the talent")
    reason_for_termination: str = Field(..., description="Reason for termination of the talent")
    notes_for_termination: str = Field(..., description="Notes for termination of the talent")
    salary: str = Field(..., description="Salary of the talent")
    hours: str = Field(..., description="Working hours of the talent")
    hourly_rate: str = Field(..., description="Hourly rate of the talent")
    
    class Config:
        """Pydantic configuration."""
        from_attributes = True