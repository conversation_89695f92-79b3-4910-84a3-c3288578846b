"""Banking information schema definitions for talent management.

This module contains Pydantic models for banking information validation,
including create, update, and response schemas.
"""

from typing import Optional
from pydantic import Field
from datetime import datetime
from app.schemas.base import CamelCaseModel


class TalentBankingInformationCreate(CamelCaseModel):
    """Schema for creating new talent banking information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        bank_name: Name of the bank
        account_number: Account number of the talent
        account_type: Type of the account
        clabe: CLABE of the talent
        swift_code: Swift code of the talent (optional)
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    bank_name: str = Field(..., max_length=100, description="Name of the bank")
    account_number: str = Field(..., max_length=50, description="Account number")
    account_type: str = Field(..., max_length=20, description="Type of the account")
    clabe: str = Field(..., max_length=100, description="CLABE number")
    swift_code: Optional[str] = Field(None, max_length=100, description="Swift code")


class TalentBankingInformationUpdate(CamelCaseModel):
    """Schema for updating existing talent banking information.
    
    All fields are optional to allow partial updates.
    """
    
    bank_name: Optional[str] = Field(None, max_length=100, description="Name of the bank")
    account_number: Optional[str] = Field(None, max_length=50, description="Account number")
    account_type: Optional[str] = Field(None, max_length=20, description="Type of the account")
    clabe: Optional[str] = Field(None, max_length=100, description="CLABE number")
    swift_code: Optional[str] = Field(None, max_length=100, description="Swift code")


class TalentBankingInformationResponse(CamelCaseModel):
    """Schema for talent banking information API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    bank_name: str = Field(..., description="Name of the bank")
    account_number: str = Field(..., description="Account number")
    account_type: str = Field(..., description="Type of the account")
    clabe: str = Field(..., description="CLABE number")
    swift_code: Optional[str] = Field(None, description="Swift code")
