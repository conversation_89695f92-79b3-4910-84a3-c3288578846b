"""Vacation mapping schema definitions for talent management.

This module contains Pydantic models for vacation mapping validation,
including create, update, and response schemas.
"""

from typing import Optional
from pydantic import Field
from datetime import datetime
from app.schemas.base import CamelCaseModel


class TalentVacationMappingCreate(CamelCaseModel):
    """Schema for creating new talent vacation mapping.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        year_number: Year number since start date
        accrued_paid_time_off_days: Accrued paid time off days
        used_paid_time_off_days: Used paid time off days
        available_paid_time_off_days: Available paid time off days
        available_bereavement_days: Available bereavement days for the year
        remaining_bereavement_days: Remaining bereavement days for the year
        available_vacation_days: Available vacation days for the year
        remaining_vacation_days: Remaining vacation days for the year
        available_parental_days: Available parental days for the year
        remaining_parental_days: Remaining parental days for the year
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    year_number: int = Field(..., description="Year number since start date")
    accrued_paid_time_off_days: float = Field(..., description="Accrued paid time off days")
    used_paid_time_off_days: float = Field(..., description="Used paid time off days")
    available_paid_time_off_days: float = Field(..., description="Available paid time off days")
    available_bereavement_days: float = Field(..., description="Available bereavement days")
    remaining_bereavement_days: float = Field(..., description="Remaining bereavement days")
    available_vacation_days: float = Field(..., description="Available vacation days")
    remaining_vacation_days: float = Field(..., description="Remaining vacation days")
    available_parental_days: float = Field(..., description="Available parental days")
    remaining_parental_days: float = Field(..., description="Remaining parental days")


class TalentVacationMappingUpdate(CamelCaseModel):
    """Schema for updating existing talent vacation mapping.
    
    All fields are optional to allow partial updates.
    """
    
    year_number: Optional[int] = Field(None, description="Year number since start date")
    accrued_paid_time_off_days: Optional[float] = Field(None, description="Accrued paid time off days")
    used_paid_time_off_days: Optional[float] = Field(None, description="Used paid time off days")
    available_paid_time_off_days: Optional[float] = Field(None, description="Available paid time off days")
    available_bereavement_days: Optional[float] = Field(None, description="Available bereavement days")
    remaining_bereavement_days: Optional[float] = Field(None, description="Remaining bereavement days")
    available_vacation_days: Optional[float] = Field(None, description="Available vacation days")
    remaining_vacation_days: Optional[float] = Field(None, description="Remaining vacation days")
    available_parental_days: Optional[float] = Field(None, description="Available parental days")
    remaining_parental_days: Optional[float] = Field(None, description="Remaining parental days")


class TalentVacationMappingResponse(CamelCaseModel):
    """Schema for talent vacation mapping API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    year_number: int = Field(..., description="Year number since start date")
    accrued_paid_time_off_days: float = Field(..., description="Accrued paid time off days")
    used_paid_time_off_days: float = Field(..., description="Used paid time off days")
    available_paid_time_off_days: float = Field(..., description="Available paid time off days")
    available_bereavement_days: float = Field(..., description="Available bereavement days")
    remaining_bereavement_days: float = Field(..., description="Remaining bereavement days")
    available_vacation_days: float = Field(..., description="Available vacation days")
    remaining_vacation_days: float = Field(..., description="Remaining vacation days")
    available_parental_days: float = Field(..., description="Available parental days")
    remaining_parental_days: float = Field(..., description="Remaining parental days")