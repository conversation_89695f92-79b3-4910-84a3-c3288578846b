"""Talent schemas package."""

from .chronic_conditions_schema import (
    ChronicConditionCreate,
    ChronicConditionUpdate,
    ChronicConditionResponse,
)
from .ongoing_health_schema import (
    OngoingHealthIssueCreate,
    OngoingHealthIssueUpdate,
    OngoingHealthIssueResponse,
)
from .past_health_schema import (
    PastHealthIssueCreate,
    PastHealthIssueUpdate,
    PastHealthIssueResponse,
)
from .talent_profile_schema import (
    ProfileCreate,
    ProfileUpdate,
    ProfileResponse,
)
from .wage_schema import (
    WageInformationCreate,
    WageInformationUpdate,
    WageInformationResponse,
    WageHistoryCreate,
    WageHistoryUpdate,
    WageHistoryResponse,
)
from .banking_schema import (
    TalentBankingInformationCreate,
    TalentBankingInformationUpdate,
    TalentBankingInformationResponse,
)
from .payroll_schema import (
    TalentPayrollInformationCreate,
    TalentPayrollInformationUpdate,
    TalentPayrollInformationResponse,
)
from .emergency_contact_schema import (
    TalentEmergencyContactCreate,
    TalentEmergencyContactUpdate,
    TalentEmergencyContactResponse,
)
from .documents_schema import (
    TalentDocumentCreate,
    TalentDocumentUpdate,
    TalentDocumentResponse,
)
from .client_info_schema import (
    TalentClientInfoCreate,
    TalentClientInfoUpdate,
    TalentClientInfoResponse,
)
from .skill_set_schema import (
    TalentSkillSetCreate,
    TalentSkillSetUpdate,
    TalentSkillSetResponse,
)
from .position_schema import (
    TalentPositionCreate,
    TalentPositionUpdate,
    TalentPositionResponse,
)
from .vacation_schema import (
    TalentVacationMappingCreate,
    TalentVacationMappingUpdate,
    TalentVacationMappingResponse,
)
from .location_schema import (
    TalentLocationMappingCreate,
    TalentLocationMappingUpdate,
    TalentLocationMappingResponse,
)

__all__ = [
    "ChronicConditionCreate",
    "ChronicConditionUpdate",
    "ChronicConditionResponse",
    "OngoingHealthIssueCreate",
    "OngoingHealthIssueUpdate",
    "OngoingHealthIssueResponse",
    "PastHealthIssueCreate",
    "PastHealthIssueUpdate",
    "PastHealthIssueResponse",
    "ProfileCreate",
    "ProfileUpdate",
    "ProfileResponse",
    "WageInformationCreate",
    "WageInformationUpdate",
    "WageInformationResponse",
    "WageHistoryCreate",
    "WageHistoryUpdate",
    "WageHistoryResponse",
    "TalentBankingInformationCreate",
    "TalentBankingInformationUpdate",
    "TalentBankingInformationResponse",
    "TalentPayrollInformationCreate",
    "TalentPayrollInformationUpdate",
    "TalentPayrollInformationResponse",
    "TalentEmergencyContactCreate",
    "TalentEmergencyContactUpdate",
    "TalentEmergencyContactResponse",
    "TalentDocumentCreate",
    "TalentDocumentUpdate",
    "TalentDocumentResponse",
    "TalentClientInfoCreate",
    "TalentClientInfoUpdate",
    "TalentClientInfoResponse",
    "TalentSkillSetCreate",
    "TalentSkillSetUpdate",
    "TalentSkillSetResponse",
    "TalentPositionCreate",
    "TalentPositionUpdate",
    "TalentPositionResponse",
    "TalentVacationMappingCreate",
    "TalentVacationMappingUpdate",
    "TalentVacationMappingResponse",
    "TalentLocationMappingCreate",
    "TalentLocationMappingUpdate",
    "TalentLocationMappingResponse",
]
