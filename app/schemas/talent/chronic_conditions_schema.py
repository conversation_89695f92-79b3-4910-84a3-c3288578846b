"""Schemas for talent chronic conditions API."""

from typing import Optional
from pydantic import Field
from app.schemas.base import CamelCaseModel


class ChronicConditionBase(CamelCaseModel):
    """Base schema for chronic condition data."""
    
    talent_profile_id: int = Field(..., description="The talent profile ID")
    condition_name: str = Field(..., min_length=1, max_length=255, description="Name of the chronic condition")
    description: Optional[str] = Field(None, max_length=1000, description="Description of the condition")
    severity: Optional[str] = Field(None, max_length=50, description="Severity level of the condition")
    medication: Optional[str] = Field(None, max_length=500, description="Current medication for the condition")
    is_active: bool = Field(True, description="Whether the condition is currently active")


class ChronicConditionCreate(ChronicConditionBase):
    """Schema for creating a new chronic condition."""
    pass


class ChronicConditionUpdate(CamelCaseModel):
    """Schema for updating an existing chronic condition."""
    
    condition_name: Optional[str] = Field(None, min_length=1, max_length=255, description="Name of the chronic condition")
    description: Optional[str] = Field(None, max_length=1000, description="Description of the condition")
    severity: Optional[str] = Field(None, max_length=50, description="Severity level of the condition")
    medication: Optional[str] = Field(None, max_length=500, description="Current medication for the condition")
    is_active: Optional[bool] = Field(None, description="Whether the condition is currently active")


class ChronicConditionResponse(ChronicConditionBase):
    """Schema for chronic condition response data."""
    
    id: int = Field(..., description="Unique identifier for the chronic condition")