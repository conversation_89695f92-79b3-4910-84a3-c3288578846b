"""Schemas for talent emergency contact information."""

from datetime import datetime
from typing import Optional

from pydantic import Field

from app.schemas.base import CamelCaseModel


class TalentEmergencyContactCreate(CamelCaseModel):
    """Schema for creating talent emergency contact information."""
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    name: str = Field(..., max_length=20, description="Name of the emergency contact")
    relationship: str = Field(..., max_length=50, description="Relationship of the emergency contact")
    phone: str = Field(..., max_length=20, description="Phone number of the emergency contact")
    notes: Optional[str] = Field(None, max_length=200, description="Note about the emergency contact")


class TalentEmergencyContactUpdate(CamelCaseModel):
    """Schema for updating talent emergency contact information."""
    
    name: Optional[str] = Field(None, max_length=20, description="Name of the emergency contact")
    relationship: Optional[str] = Field(None, max_length=50, description="Relationship of the emergency contact")
    phone: Optional[str] = Field(None, max_length=20, description="Phone number of the emergency contact")
    notes: Optional[str] = Field(None, max_length=200, description="Note about the emergency contact")


class TalentEmergencyContactResponse(CamelCaseModel):
    """Schema for talent emergency contact information API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    name: str = Field(..., description="Name of the emergency contact")
    relationship: str = Field(..., description="Relationship of the emergency contact")
    phone: str = Field(..., description="Phone number of the emergency contact")
    notes: Optional[str] = Field(None, description="Note about the emergency contact")