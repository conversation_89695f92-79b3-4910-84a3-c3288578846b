"""Client information schema definitions for talent management.

This module contains Pydantic models for talent client information validation,
including create, update, and response schemas.
"""

from typing import Optional
from pydantic import Field
from app.schemas.base import CamelCaseModel


class TalentClientInfoCreate(CamelCaseModel):
    """Schema for creating new talent client information.
    
    Attributes:
        talent_profile_id: Foreign key reference to the associated talent profile
        campaign: Campaign of the talent
        bpo_manager: BPO manager of the talent
        client_id: Client id of the talent
        reporting_department: Reporting department of the talent
    """
    
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    campaign: Optional[str] = Field(None, description="Campaign of the talent")
    bpo_manager: Optional[str] = Field(None, description="BPO manager of the talent")
    client_id: int = Field(..., description="Client id of the talent")
    reporting_department: Optional[str] = Field(None, description="Reporting department of the talent")


class TalentClientInfoUpdate(CamelCaseModel):
    """Schema for updating existing talent client information.
    
    All fields are optional to allow partial updates.
    """
    
    campaign: Optional[str] = Field(None, description="Campaign of the talent")
    bpo_manager: Optional[str] = Field(None, description="BPO manager of the talent")
    client_id: Optional[int] = Field(None, description="Client id of the talent")
    reporting_department: Optional[str] = Field(None, description="Reporting department of the talent")


class TalentClientInfoResponse(CamelCaseModel):
    """Schema for talent client information API responses.
    
    Includes all fields from the database model.
    """
    
    id: int = Field(..., description="Primary key")
    talent_profile_id: int = Field(..., description="Foreign key reference to talent profile")
    campaign: Optional[str] = Field(None, description="Campaign of the talent")
    bpo_manager: Optional[str] = Field(None, description="BPO manager of the talent")
    client_id: int = Field(..., description="Client id of the talent")
    reporting_department: Optional[str] = Field(None, description="Reporting department of the talent")
