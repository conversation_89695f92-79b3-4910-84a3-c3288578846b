from typing import Annotated
from fastapi import APIRouter, Depends, Request, HTTPException, status

from app.services import ProfileService
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error


router = APIRouter()

service_dependency = Annotated[ProfileService, Depends()]


@router.get("/profile", response_model=GeneralResponse)
async def get_profile(
    request: Request,
    user_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get user profile information.
    
    Args:
        request: FastAPI request object
        user_id: The user ID
        service: Injected profile service
        
    Returns:
        GeneralResponse with profile data
    """
    try:
        profile = service.get_profile()
        
        if not profile:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Profile not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Profile retrieved successfully",
            data=profile.model_dump() if hasattr(profile, 'model_dump') else profile
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
