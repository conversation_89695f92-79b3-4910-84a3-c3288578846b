"""API router for talent past health issues management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.db import TalentPastHealthIssues
from app.response_models.general_response import GeneralResponse
from app.services.talent import TalentPastHealthService

router = APIRouter(prefix="/past-health", tags=["Talent Past Health Issues"])

service_dependency = Annotated[TalentPastHealthService, Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_past_health_issue(
    request: Request,
    past_health_issue: TalentPastHealthIssues,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new past health issue for a talent.
    
    Args:
        past_health_issue: The past health issue data to create
        service: Injected past health service
        
    Returns:
        GeneralResponse with created past health issue data
    """
    try:
        data = service.create_past_health_issue(past_health_issue)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Past health issue created successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}", response_model=GeneralResponse)
async def get_past_health_issues_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all past health issues for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected past health service
        
    Returns:
        GeneralResponse with list of past health issues
    """
    try:
        data = service.get_past_health_issues_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Past health issues retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{issue_id}", response_model=GeneralResponse)
async def get_past_health_issue_by_id(
    request: Request,
    issue_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific past health issue by ID.
    
    Args:
        issue_id: The past health issue ID
        service: Injected past health service
        
    Returns:
        GeneralResponse with past health issue data
    """
    try:
        data = service.get_past_health_issue_by_id(issue_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Past health issue retrieved successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{issue_id}", response_model=GeneralResponse)
async def update_past_health_issue(
    request: Request,
    issue_id: int,
    past_health_issue: TalentPastHealthIssues,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing past health issue.
    
    Args:
        issue_id: The past health issue ID to update
        past_health_issue: The updated past health issue data
        service: Injected past health service
        
    Returns:
        GeneralResponse with updated past health issue data
    """
    try:
        # Set the ID to ensure we're updating the correct record
        past_health_issue.id = issue_id
        data = service.update_past_health_issue(past_health_issue)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Past health issue updated successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{issue_id}", response_model=GeneralResponse)
async def delete_past_health_issue(
    request: Request,
    issue_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a past health issue.
    
    Args:
        issue_id: The past health issue ID to delete
        service: Injected past health service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_past_health_issue(issue_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Past health issue deleted successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
