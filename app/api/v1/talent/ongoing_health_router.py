"""API router for talent ongoing health issues management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.db import TalentOngoingHealthIssues
from app.response_models.general_response import GeneralResponse
from app.services.talent import TalentOngoingHealthService

router = APIRouter(prefix="/ongoing-health", tags=["Talent Ongoing Health Issues"])

service_dependency = Annotated[TalentOngoingHealthService, Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_ongoing_health_issue(
    request: Request,
    ongoing_health_issue: TalentOngoingHealthIssues,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new ongoing health issue for a talent.
    
    Args:
        ongoing_health_issue: The ongoing health issue data to create
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse with created ongoing health issue data
    """
    try:
        data = service.create_ongoing_health_issue(ongoing_health_issue)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Ongoing health issue created successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}", response_model=GeneralResponse)
async def get_ongoing_health_issues_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all ongoing health issues for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse with list of ongoing health issues
    """
    try:
        data = service.get_ongoing_health_issues_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Ongoing health issues retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{issue_id}", response_model=GeneralResponse)
async def get_ongoing_health_issue_by_id(
    request: Request,
    issue_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific ongoing health issue by ID.
    
    Args:
        issue_id: The ongoing health issue ID
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse with ongoing health issue data
    """
    try:
        data = service.get_ongoing_health_issue_by_id(issue_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Ongoing health issue retrieved successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{issue_id}", response_model=GeneralResponse)
async def update_ongoing_health_issue(
    request: Request,
    issue_id: int,
    ongoing_health_issue: TalentOngoingHealthIssues,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing ongoing health issue.
    
    Args:
        issue_id: The ongoing health issue ID to update
        ongoing_health_issue: The updated ongoing health issue data
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse with updated ongoing health issue data
    """
    try:
        # Set the ID to ensure we're updating the correct record
        ongoing_health_issue.id = issue_id
        data = service.update_ongoing_health_issue(ongoing_health_issue)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Ongoing health issue updated successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{issue_id}", response_model=GeneralResponse)
async def delete_ongoing_health_issue(
    request: Request,
    issue_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete an ongoing health issue.
    
    Args:
        issue_id: The ongoing health issue ID to delete
        service: Injected ongoing health service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_ongoing_health_issue(issue_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Ongoing health issue deleted successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
