"""Talent Equipment Mapping API router."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from app.services.talent.equipment_mapping_service import TalentEquipmentMappingService
from app.schemas.talent.equipment_mapping_schema import (
    TalentEquipmentMappingCreate,
    TalentEquipmentMappingUpdate
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[TalentEquipmentMappingService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_equipment_mapping(
    request: Request,
    mapping_data: TalentEquipmentMappingCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new talent equipment mapping."""
    try:
        mapping = service.create_equipment_mapping(mapping_data)
        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Equipment mapping already exists for this talent and equipment"
            )
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Equipment mapping created successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_equipment_mappings(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all talent equipment mappings with pagination."""
    try:
        mappings = service.get_all_equipment_mappings(skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mappings retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/active", response_model=GeneralResponse)
async def get_active_equipment_mappings(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all active (not replaced) talent equipment mappings."""
    try:
        mappings = service.get_active_equipment_mappings(skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Active equipment mappings retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_equipment_mappings_by_talent(
    request: Request,
    talent_profile_id: int,
    service: __service,
) -> GeneralResponse:
    """Get equipment mappings for a specific talent."""
    try:
        mappings = service.get_equipment_mappings_by_talent_id(talent_profile_id)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mappings for talent retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/equipment/{equipment_id}", response_model=GeneralResponse)
async def get_equipment_mappings_by_equipment(
    request: Request,
    equipment_id: int,
    service: __service,
) -> GeneralResponse:
    """Get talent mappings for a specific equipment."""
    try:
        mappings = service.get_equipment_mappings_by_equipment_id(equipment_id)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mappings for equipment retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{mapping_id}", response_model=GeneralResponse)
async def get_equipment_mapping_by_id(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Get equipment mapping by ID."""
    try:
        mapping = service.get_equipment_mapping_by_id(mapping_id)
        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Equipment mapping not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mapping retrieved successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{mapping_id}", response_model=GeneralResponse)
async def update_equipment_mapping(
    request: Request,
    mapping_id: int,
    mapping_data: TalentEquipmentMappingUpdate,
    service: __service,
) -> GeneralResponse:
    """Update equipment mapping information."""
    try:
        mapping = service.update_equipment_mapping(mapping_id, mapping_data)
        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Equipment mapping not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mapping updated successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{mapping_id}", response_model=GeneralResponse)
async def delete_equipment_mapping(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Delete an equipment mapping."""
    try:
        success = service.delete_equipment_mapping(mapping_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Equipment mapping not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Equipment mapping deleted successfully",
            data=None
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{mapping_id}/toggle-replacement", response_model=GeneralResponse)
async def toggle_equipment_replacement_status(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Toggle equipment replacement status."""
    try:
        # Get current mapping
        current_mapping = service.get_equipment_mapping_by_id(mapping_id)
        if not current_mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Equipment mapping not found"
            )
        
        # Toggle the replacement status
        update_data = TalentEquipmentMappingUpdate(
            is_replaced=not current_mapping.is_replaced
        )
        mapping = service.update_equipment_mapping(mapping_id, update_data)
        
        if not mapping:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update equipment mapping"
            )
        
        status_text = "marked as replaced" if mapping.is_replaced else "marked as active"
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Equipment mapping {status_text} successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
