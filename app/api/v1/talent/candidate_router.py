"""API router for talent profile management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent.talent_profile_schema import Profile<PERSON><PERSON>, ProfileUpdate
from app.services.talent import TalentService

router = APIRouter(prefix="/talents", tags=["Talent Profiles"])

service_dependency = Annotated[TalentService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_talent_profile(
    request: Request,
    talent_profile: ProfileCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new talent profile.
    
    Args:
        talent_profile: The talent profile data to create
        service: Injected talent service
        
    Returns:
        GeneralResponse with created talent profile data
    """
    try:
        talent = service.create_talent_profile_from_schema(talent_profile)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Talent profile created successfully",
            data=talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_talent_profiles(
    request: Request,
    service: service_dependency,
) -> GeneralResponse:
    """Get all talent profiles.
    
    Args:
        service: Injected talent service
        
    Returns:
        GeneralResponse with list of talent profiles
    """
    try:
        talents = service.get_all_talents()
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profiles retrieved successfully",
            data=talents
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{talent_id}", response_model=GeneralResponse)
async def get_talent_profile_by_id(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific talent profile by ID.
    
    Args:
        talent_id: The talent profile ID
        service: Injected talent service
        
    Returns:
        GeneralResponse with talent profile data
    """
    try:
        talent = service.get_talent_by_id(talent_id)
        if talent is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile retrieved successfully",
            data=talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{talent_id}", response_model=GeneralResponse)
async def update_talent_profile(
    request: Request,
    talent_id: int,
    talent_profile: ProfileUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing talent profile.
    
    Args:
        talent_id: The talent profile ID to update
        talent_profile: The updated talent profile data
        service: Injected talent service
        
    Returns:
        GeneralResponse with updated talent profile data
    """
    try:
        updated_talent = service.update_talent_profile_from_schema(talent_id, talent_profile)
        if updated_talent is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile updated successfully",
            data=updated_talent
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{talent_id}", response_model=GeneralResponse)
async def delete_talent_profile(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a talent profile.
    
    Args:
        talent_id: The talent profile ID to delete
        service: Injected talent service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        success = service.delete_talent_profile(talent_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent profile deleted successfully",
            data=None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
