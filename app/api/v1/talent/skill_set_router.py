"""Skill set router for talent management API.

This module contains the FastAPI router for talent skill set endpoints.
"""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentSkillSetCreate,
    TalentSkillSetUpdate,
)
from app.services.talent.skill_set_service import TalentSkillSetService

router = APIRouter(prefix="/skill-sets")

service_dependency = Annotated[TalentSkillSetService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_skill_set(
    request: Request,
    skill_set_data: TalentSkillSetCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new skill set record for a talent.
    
    Args:
        request: FastAPI request object
        skill_set_data: The skill set data to create
        service: Injected skill set service
        
    Returns:
        GeneralResponse with created skill set data
    """
    try:
        skill_set = service.create_skill_set(skill_set_data)
        
        if not skill_set:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create skill set. Skill set may already exist for this talent."
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Skill set created successfully",
            data=skill_set.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_skill_sets_by_talent(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all skill sets for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: Talent profile ID
        service: Injected skill set service
        
    Returns:
        GeneralResponse with list of skill sets for the talent
    """
    try:
        skill_sets = service.get_skill_sets_by_talent_id(talent_profile_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(skill_sets)} skill set(s) for talent {talent_profile_id}",
            data=[skill_set.model_dump() for skill_set in skill_sets]
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_skill_sets(
    request: Request,
    service: service_dependency,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return")
) -> GeneralResponse:
    """Get all talent skill sets with pagination.
    
    Args:
        request: FastAPI request object
        service: Injected skill set service
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        GeneralResponse with paginated list of skill sets
    """
    try:
        skill_sets = service.get_all_skill_sets(skip=skip, limit=limit)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(skill_sets)} skill set(s) (page {skip // limit + 1})",
            data=[skill_set.model_dump() for skill_set in skill_sets]
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{skill_set_id}", response_model=GeneralResponse)
async def get_skill_set_by_id(
    request: Request,
    skill_set_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a talent skill set by ID.
    
    Args:
        request: FastAPI request object
        skill_set_id: Skill set ID
        service: Injected skill set service
        
    Returns:
        GeneralResponse with skill set data
        
    Raises:
        HTTPException: If skill set not found
    """
    try:
        skill_set = service.get_skill_set_by_id(skill_set_id)
        if not skill_set:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent skill set with ID {skill_set_id} not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent skill set retrieved successfully",
            data=skill_set.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{skill_set_id}", response_model=GeneralResponse)
async def update_skill_set(
    request: Request,
    skill_set_id: int,
    skill_set_data: TalentSkillSetUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing talent skill set.
    
    Args:
        request: FastAPI request object
        skill_set_id: Skill set ID to update
        skill_set_data: Updated skill set data
        service: Injected skill set service
        
    Returns:
        GeneralResponse with updated skill set data
        
    Raises:
        HTTPException: If skill set not found or update fails
    """
    try:
        skill_set = service.update_skill_set(skill_set_id, skill_set_data)
        if not skill_set:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent skill set with ID {skill_set_id} not found or update failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent skill set updated successfully",
            data=skill_set.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{skill_set_id}", response_model=GeneralResponse)
async def delete_skill_set(
    request: Request,
    skill_set_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a talent skill set.
    
    Args:
        request: FastAPI request object
        skill_set_id: Skill set ID to delete
        service: Injected skill set service
        
    Returns:
        GeneralResponse with deletion status
        
    Raises:
        HTTPException: If skill set not found or deletion fails
    """
    try:
        success = service.delete_skill_set(skill_set_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent skill set with ID {skill_set_id} not found or deletion failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent skill set deleted successfully",
            data=True
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_profile_id}/exists", response_model=GeneralResponse)
async def check_skill_sets_exist(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Check if skill set records exist for a talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: Talent profile ID
        service: Injected skill set service
        
    Returns:
        GeneralResponse with existence status
    """
    try:
        exists = service.skill_set_exists_for_talent(talent_profile_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Skill set existence check for talent {talent_profile_id} completed",
            data=exists
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
