"""Position router for talent management API.

This module contains the FastAPI router for talent position endpoints.
"""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentPositionCreate,
    TalentPositionUpdate,
)
from app.services.talent.position_service import TalentPositionService


router = APIRouter(prefix="/positions")

service_dependency = Annotated[TalentPositionService, Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_position(
    request: Request,
    position_data: TalentPositionCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new position record for a talent.
    
    Args:
        request: FastAPI request object
        position_data: The position data to create
        service: Injected position service
        
    Returns:
        GeneralResponse with created position data
    """
    try:
        position = service.create_position(position_data)
        
        if not position:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create position. Position may already exist for this talent."
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Position created successfully",
            data=position.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_positions_by_talent(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all positions for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: Talent profile ID
        service: Injected position service
        
    Returns:
        GeneralResponse with list of positions for the talent
    """
    try:
        positions = service.get_positions_by_talent_id(talent_profile_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(positions)} position(s) for talent {talent_profile_id}",
            data=[position.model_dump() for position in positions]
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_positions(
    request: Request,
    service: service_dependency,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all talent positions with pagination.
    
    Args:
        request: FastAPI request object
        service: Injected position service
        skip: Number of records to skip
        limit: Maximum number of records to return
        
    Returns:
        GeneralResponse with paginated list of positions
    """
    try:
        positions = service.get_all_positions(skip=skip, limit=limit)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Retrieved {len(positions)} position(s) (page {skip // limit + 1})",
            data=[position.model_dump() for position in positions]
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{position_id}", response_model=GeneralResponse)
async def get_position_by_id(
    request: Request,
    position_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a talent position by ID.
    
    Args:
        request: FastAPI request object
        position_id: Position ID
        service: Injected position service
        
    Returns:
        GeneralResponse with position data
        
    Raises:
        HTTPException: If position not found
    """
    try:
        position = service.get_position_by_id(position_id)
        if not position:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent position with ID {position_id} not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent position retrieved successfully",
            data=position.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{position_id}", response_model=GeneralResponse)
async def update_position(
    request: Request,
    position_id: int,
    position_data: TalentPositionUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing talent position.
    
    Args:
        request: FastAPI request object
        position_id: Position ID to update
        position_data: Updated position data
        service: Injected position service
        
    Returns:
        GeneralResponse with updated position data
        
    Raises:
        HTTPException: If position not found or update fails
    """
    try:
        position = service.update_position(position_id, position_data)
        if not position:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent position with ID {position_id} not found or update failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent position updated successfully",
            data=position.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{position_id}", response_model=GeneralResponse)
async def delete_position(
    request: Request,
    position_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a talent position.
    
    Args:
        request: FastAPI request object
        position_id: Position ID to delete
        service: Injected position service
        
    Returns:
        GeneralResponse with deletion status
        
    Raises:
        HTTPException: If position not found or deletion fails
    """
    try:
        success = service.delete_position(position_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Talent position with ID {position_id} not found or deletion failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Talent position deleted successfully",
            data=True
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_profile_id}/exists", response_model=GeneralResponse)
async def check_positions_exist(
    request: Request,
    talent_profile_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Check if position records exist for a talent.
    
    Args:
        request: FastAPI request object
        talent_profile_id: Talent profile ID
        service: Injected position service
        
    Returns:
        GeneralResponse with existence status
    """
    try:
        exists = service.position_exists_for_talent(talent_profile_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Position existence check for talent {talent_profile_id} completed",
            data=exists
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
