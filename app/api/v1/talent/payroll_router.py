"""API router for talent payroll information management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentPayrollInformationCreate,
    TalentPayrollInformationUpdate,
    TalentPayrollInformationResponse,
)
from app.services.talent import PayrollService

router = APIRouter(prefix="/payroll")
__service = Annotated[PayrollService, Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_payroll_information(
    request: Request,
    payroll_data: TalentPayrollInformationCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new payroll information record for a talent.
    
    Args:
        request: FastAPI request object
        payroll_data: The payroll information data to create
        service: Injected payroll service
        
    Returns:
        GeneralResponse with created payroll information data
    """
    try:
        data = service.create_payroll_information(payroll_data)
        if data:
            response_data = TalentPayrollInformationResponse.model_validate(data)
            return GeneralResponse(
                status_code=status.HTTP_201_CREATED,
                message="Payroll information created successfully",
                data=response_data.model_dump(),
            )
        else:
            return GeneralResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                message="Payroll information already exists for this talent",
                data=None,
            )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}", response_model=GeneralResponse)
async def get_payroll_information_by_talent(
    request: Request,
    talent_id: int,
    service: __service,
) -> GeneralResponse:
    """Get payroll information for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected payroll service
        
    Returns:
        GeneralResponse with payroll information data
    """
    try:
        data = service.get_payroll_information_by_talent_id(talent_id)
        if data:
            response_data = TalentPayrollInformationResponse.model_validate(data)
            return GeneralResponse(
                status_code=status.HTTP_200_OK,
                message="Payroll information retrieved successfully",
                data=response_data.model_dump(),
            )
        else:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="Payroll information not found",
                data=None,
            )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{payroll_id}", response_model=GeneralResponse)
async def get_payroll_information_by_id(
    request: Request,
    payroll_id: int,
    service: __service,
) -> GeneralResponse:
    """Get a specific payroll information by ID.
    
    Args:
        request: FastAPI request object
        payroll_id: The payroll information ID
        service: Injected payroll service
        
    Returns:
        GeneralResponse with payroll information data
    """
    try:
        data = service.get_payroll_information_by_id(payroll_id)
        response_data = TalentPayrollInformationResponse.model_validate(data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Payroll information retrieved successfully",
            data=response_data.model_dump(),
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{payroll_id}", response_model=GeneralResponse)
async def update_payroll_information(
    request: Request,
    payroll_id: int,
    payroll_data: TalentPayrollInformationUpdate,
    service: __service,
) -> GeneralResponse:
    """Update an existing payroll information record.
    
    Args:
        request: FastAPI request object
        payroll_id: The payroll information ID to update
        payroll_data: The updated payroll information data
        service: Injected payroll service
        
    Returns:
        GeneralResponse with updated payroll information data
    """
    try:
        data = service.update_payroll_information(payroll_id, payroll_data)
        if data:
            response_data = TalentPayrollInformationResponse.model_validate(data)
            return GeneralResponse(
                status_code=status.HTTP_200_OK,
                message="Payroll information updated successfully",
                data=response_data.model_dump(),
            )
        else:
            return GeneralResponse(
                status_code=status.HTTP_400_BAD_REQUEST,
                message="Payroll information update failed",
                data=None,
            )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{payroll_id}", response_model=GeneralResponse)
async def delete_payroll_information(
    request: Request,
    payroll_id: int,
    service: __service,
) -> GeneralResponse:
    """Delete a payroll information.
    
    Args:
        request: FastAPI request object
        payroll_id: The payroll information ID to delete
        service: Injected payroll service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_payroll_information(payroll_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Payroll information deleted successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
