"""Talent Software Mapping API router."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from app.services.talent.software_mapping_service import TalentSoftwareMappingService
from app.schemas.talent.software_mapping_schema import (
    TalentSoftwareMappingCreate,
    TalentSoftwareMappingUpdate
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[TalentSoftwareMappingService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_software_mapping(
    request: Request,
    mapping_data: TalentSoftwareMappingCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new software mapping."""
    try:
        mapping = service.create_software_mapping(mapping_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Software mapping created successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_software_mappings(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all software mappings with pagination."""
    try:
        mappings = service.get_all_software_mappings(skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mappings retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_software_mappings_by_talent(
    request: Request,
    talent_profile_id: int,
    service: __service,
) -> GeneralResponse:
    """Get all software mappings for a specific talent profile."""
    try:
        mappings = service.get_software_mappings_by_talent(talent_profile_id)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mappings retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/software/{software}", response_model=GeneralResponse)
async def get_software_mappings_by_software(
    request: Request,
    software: str,
    service: __service,
) -> GeneralResponse:
    """Get all mappings for a specific software."""
    try:
        mappings = service.get_software_mappings_by_software(software)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mappings retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/software/{software}/version/{version}", response_model=GeneralResponse)
async def get_software_mappings_by_version(
    request: Request,
    software: str,
    version: str,
    service: __service,
) -> GeneralResponse:
    """Get all mappings for a specific software and version."""
    try:
        mappings = service.get_software_mappings_by_version(software, version)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mappings retrieved successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/search", response_model=GeneralResponse)
async def search_software_mappings_by_software(
    request: Request,
    service: __service,
    software_pattern: str = Query(..., description="Software name pattern to search for"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Search software mappings by software name pattern."""
    try:
        mappings = service.search_software_mappings_by_software(software_pattern, skip=skip, limit=limit)
        mapping_responses = [mapping.model_dump() for mapping in mappings]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mappings search completed successfully",
            data=mapping_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/count", response_model=GeneralResponse)
async def get_software_mapping_count(
    request: Request,
    service: __service,
) -> GeneralResponse:
    """Get total count of software mappings."""
    try:
        count = service.get_software_mapping_count()
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mapping count retrieved successfully",
            data={"total_count": count}
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/count/talent/{talent_profile_id}", response_model=GeneralResponse)
async def get_software_mapping_count_by_talent(
    request: Request,
    talent_profile_id: int,
    service: __service,
) -> GeneralResponse:
    """Get count of software mappings for a specific talent profile."""
    try:
        count = service.get_software_mapping_count_by_talent(talent_profile_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mapping count retrieved successfully",
            data={"talent_profile_id": talent_profile_id, "count": count}
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{mapping_id}", response_model=GeneralResponse)
async def get_software_mapping_by_id(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Get software mapping by ID."""
    try:
        mapping = service.get_software_mapping_by_id(mapping_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mapping retrieved successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{mapping_id}", response_model=GeneralResponse)
async def update_software_mapping(
    request: Request,
    mapping_id: int,
    mapping_data: TalentSoftwareMappingUpdate,
    service: __service,
) -> GeneralResponse:
    """Update software mapping information."""
    try:
        mapping = service.update_software_mapping(mapping_id, mapping_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mapping updated successfully",
            data=mapping.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{mapping_id}", response_model=GeneralResponse)
async def delete_software_mapping(
    request: Request,
    mapping_id: int,
    service: __service,
) -> GeneralResponse:
    """Delete a software mapping."""
    try:
        service.delete_software_mapping(mapping_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Software mapping deleted successfully",
            data=None
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )