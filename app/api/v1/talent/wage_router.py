"""API router for talent wage information management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    WageInformationCreate,
    WageInformationUpdate,
    WageHistoryCreate,
    WageHistoryUpdate,
)
from app.services.talent import WageService

router = APIRouter(prefix="/wage")

service_dependency = Annotated[WageService, Depends()]


# Wage Information Endpoints

@router.post("/info", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_wage_info(
    request: Request,
    wage_info_data: WageInformationCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new wage information record for a talent.
    
    Args:
        request: FastAPI request object
        wage_info_data: The wage information data to create
        service: Injected wage service
        
    Returns:
        GeneralResponse with created wage information data
    """
    try:
        # Convert schema to model
        from app.db import TalentWageInformation
        wage_info = TalentWageInformation(**wage_info_data.model_dump())
        
        created_wage_info = service.create_wage_info(wage_info)
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Wage information created successfully",
            data=created_wage_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/info/talent/{talent_id}", response_model=GeneralResponse)
async def get_wage_info_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get wage information for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected wage service
        
    Returns:
        GeneralResponse with wage information data
    """
    try:
        wage_info = service.get_wage_info_by_talent_id(talent_id)
        
        if not wage_info:
            return GeneralResponse(
                status_code=status.HTTP_404_NOT_FOUND,
                message="No wage information found for this talent",
                data=None
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage information retrieved successfully",
            data=wage_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/info/{wage_id}", response_model=GeneralResponse)
async def get_wage_info_by_id(
    request: Request,
    wage_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific wage information by ID.
    
    Args:
        request: FastAPI request object
        wage_id: The wage information ID
        service: Injected wage service
        
    Returns:
        GeneralResponse with wage information data
    """
    try:
        wage_info = service.get_wage_info_by_id(wage_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage information retrieved successfully",
            data=wage_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/info/{wage_id}", response_model=GeneralResponse)
async def update_wage_info(
    request: Request,
    wage_id: int,
    wage_info_data: WageInformationUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing wage information record.
    
    Args:
        request: FastAPI request object
        wage_id: The wage information ID to update
        wage_info_data: The updated wage information data
        service: Injected wage service
        
    Returns:
        GeneralResponse with updated wage information data
    """
    try:
        # Convert schema to model with ID
        from app.db import TalentWageInformation
        wage_info = TalentWageInformation(id=wage_id, **wage_info_data.model_dump())
        
        updated_wage_info = service.update_wage_info(wage_info)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage information updated successfully",
            data=updated_wage_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/info/{wage_id}", response_model=GeneralResponse)
async def delete_wage_info(
    request: Request,
    wage_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a wage information record.
    
    Args:
        request: FastAPI request object
        wage_id: The wage information ID to delete
        service: Injected wage service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_wage_info(wage_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage information deleted successfully",
            data={"deleted": True}
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


# Wage History Endpoints

@router.post("/history", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_wage_history(
    request: Request,
    wage_history_data: WageHistoryCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new wage history record.
    
    Args:
        request: FastAPI request object
        wage_history_data: The wage history data to create
        service: Injected wage service
        
    Returns:
        GeneralResponse with created wage history data
    """
    try:
        # Convert schema to model
        from app.db import TalentWageHistory
        wage_history = TalentWageHistory(**wage_history_data.model_dump())
        
        created_wage_history = service.create_wage_history(wage_history)
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Wage history created successfully",
            data=created_wage_history.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/history/wage/{wage_id}", response_model=GeneralResponse)
async def get_wage_history_by_wage_id(
    request: Request,
    wage_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get wage history for a specific wage information.
    
    Args:
        request: FastAPI request object
        wage_id: The wage information ID
        service: Injected wage service
        
    Returns:
        GeneralResponse with list of wage history data
    """
    try:
        wage_history_list = service.get_wage_history_by_wage_id(wage_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage history retrieved successfully",
            data=[wage_history.model_dump() for wage_history in wage_history_list]
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/history/{history_id}", response_model=GeneralResponse)
async def get_wage_history_by_id(
    request: Request,
    history_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific wage history by ID.
    
    Args:
        request: FastAPI request object
        history_id: The wage history ID
        service: Injected wage service
        
    Returns:
        GeneralResponse with wage history data
    """
    try:
        wage_history = service.get_wage_history_by_id(history_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage history retrieved successfully",
            data=wage_history.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/history/{history_id}", response_model=GeneralResponse)
async def update_wage_history(
    request: Request,
    history_id: int,
    wage_history_data: WageHistoryUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing wage history record.
    
    Args:
        request: FastAPI request object
        history_id: The wage history ID to update
        wage_history_data: The updated wage history data
        service: Injected wage service
        
    Returns:
        GeneralResponse with updated wage history data
    """
    try:
        # Convert schema to model with ID
        from app.db import TalentWageHistory
        wage_history = TalentWageHistory(id=history_id, **wage_history_data.model_dump())
        
        updated_wage_history = service.update_wage_history(wage_history)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage history updated successfully",
            data=updated_wage_history.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/history/{history_id}", response_model=GeneralResponse)
async def delete_wage_history(
    request: Request,
    history_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a wage history record.
    
    Args:
        request: FastAPI request object
        history_id: The wage history ID to delete
        service: Injected wage service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_wage_history(history_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Wage history deleted successfully",
            data={"deleted": True}
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=None,
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
