"""API router for talent vacation mapping management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentVacationMappingCreate,
    TalentVacationMappingUpdate,
)
from app.services.talent import VacationService

router = APIRouter(prefix="/vacation")

service_dependency = Annotated[VacationService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_vacation_mapping(
    request: Request,
    vacation_data: TalentVacationMappingCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new vacation mapping record for a talent.
    
    Args:
        request: FastAPI request object
        vacation_data: The vacation mapping data to create
        service: Injected vacation service
        
    Returns:
        GeneralResponse with created vacation mapping data
    """
    try:
        vacation_mapping = service.create_vacation_mapping(vacation_data)
        
        if not vacation_mapping:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create vacation mapping. Vacation mapping may already exist for this talent and year."
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Vacation mapping created successfully",
            data=vacation_mapping.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}", response_model=GeneralResponse)
async def get_vacation_mapping_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get vacation mappings for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected vacation service
        
    Returns:
        GeneralResponse with vacation mapping data
    """
    try:
        vacation_mappings = service.get_vacation_mapping_by_talent_id(talent_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Vacation mappings retrieved successfully",
            data=[vacation_mapping.model_dump() for vacation_mapping in vacation_mappings]
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}/year/{year_number}", response_model=GeneralResponse)
async def get_vacation_mapping_by_talent_and_year(
    request: Request,
    talent_id: int,
    year_number: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get vacation mapping for a specific talent and year.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        year_number: The year number
        service: Injected vacation service
        
    Returns:
        GeneralResponse with vacation mapping data
    """
    try:
        vacation_mapping = service.get_vacation_mapping_by_talent_and_year(talent_id, year_number)
        
        if not vacation_mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vacation mapping not found for this talent and year"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Vacation mapping retrieved successfully",
            data=vacation_mapping.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{vacation_id}", response_model=GeneralResponse)
async def get_vacation_mapping_by_id(
    request: Request,
    vacation_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific vacation mapping by ID.
    
    Args:
        request: FastAPI request object
        vacation_id: The vacation mapping ID
        service: Injected vacation service
        
    Returns:
        GeneralResponse with vacation mapping data
    """
    try:
        vacation_mapping = service.get_vacation_mapping_by_id(vacation_id)
        
        if not vacation_mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vacation mapping not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Vacation mapping retrieved successfully",
            data=vacation_mapping.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{vacation_id}", response_model=GeneralResponse)
async def update_vacation_mapping(
    request: Request,
    vacation_id: int,
    vacation_data: TalentVacationMappingUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing vacation mapping record.
    
    Args:
        request: FastAPI request object
        vacation_id: The vacation mapping ID to update
        vacation_data: The updated vacation mapping data
        service: Injected vacation service
        
    Returns:
        GeneralResponse with updated vacation mapping data
    """
    try:
        vacation_mapping = service.update_vacation_mapping(vacation_id, vacation_data)
        
        if not vacation_mapping:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vacation mapping not found or update failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Vacation mapping updated successfully",
            data=vacation_mapping.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{vacation_id}", response_model=GeneralResponse)
async def delete_vacation_mapping(
    request: Request,
    vacation_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a vacation mapping record.
    
    Args:
        request: FastAPI request object
        vacation_id: The vacation mapping ID to delete
        service: Injected vacation service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        success = service.delete_vacation_mapping(vacation_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Vacation mapping not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Vacation mapping deleted successfully",
            data=None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )