"""API router for talent chronic conditions management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Request, status

from app.core import log_api_error
from app.db import TalentCronicConditions
from app.response_models.general_response import GeneralResponse
from app.services.talent import TalentChronicConditionsService

router = APIRouter(prefix="/chronic-conditions", tags=["Talent Chronic Conditions"])

service_dependency = Annotated[TalentChronicConditionsService, Depends()]

@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_chronic_condition(
    request: Request,
    chronic_condition: TalentCronicConditions,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new chronic condition for a talent.
    
    Args:
        chronic_condition: The chronic condition data to create
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse with created chronic condition data
    """
    try:
        data = service.create_chronic_condition(chronic_condition)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Chronic condition created successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint="/chronic-conditions/",
            method="POST",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}", response_model=GeneralResponse)
async def get_chronic_conditions_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get all chronic conditions for a specific talent.
    
    Args:
        talent_id: The talent profile ID
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse with list of chronic conditions
    """
    try:
        data = service.get_chronic_conditions_by_talent_id(talent_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Chronic conditions retrieved successfully",
            data=data,
        )
    except Exception as e:
        log_api_error(
            endpoint=f"/chronic-conditions/talent/{talent_id}",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{condition_id}", response_model=GeneralResponse)
async def get_chronic_condition_by_id(
    request: Request,
    condition_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific chronic condition by ID.
    
    Args:
        condition_id: The chronic condition ID
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse with chronic condition data
    """
    try:
        data = service.get_chronic_condition_by_id(condition_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Chronic condition retrieved successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/chronic-conditions/{condition_id}",
            method="GET",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{condition_id}", response_model=GeneralResponse)
async def update_chronic_condition(
    request: Request,
    condition_id: int,
    chronic_condition: TalentCronicConditions,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing chronic condition.
    
    Args:
        condition_id: The chronic condition ID to update
        chronic_condition: The updated chronic condition data
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse with updated chronic condition data
    """
    try:
        # Set the ID to ensure we're updating the correct record
        chronic_condition.id = condition_id
        data = service.update_chronic_condition(chronic_condition)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Chronic condition updated successfully",
            data=data,
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/chronic-conditions/{condition_id}",
            method="PUT",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{condition_id}", response_model=GeneralResponse)
async def delete_chronic_condition(
    request: Request,
    condition_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a chronic condition.
    
    Args:
        condition_id: The chronic condition ID to delete
        service: Injected chronic conditions service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        service.delete_chronic_condition(condition_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Chronic condition deleted successfully",
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=f"/chronic-conditions/{condition_id}",
            method="DELETE",
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )
