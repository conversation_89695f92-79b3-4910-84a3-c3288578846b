"""API router for talent client information management."""

from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, Query, Request, status

from app.core import log_api_error
from app.response_models.general_response import GeneralResponse
from app.schemas.talent import (
    TalentClientInfoCreate,
    TalentClientInfoUpdate,
)
from app.services.talent.client_info_service import TalentClientInfoService

router = APIRouter(prefix="/client-info")

service_dependency = Annotated[TalentClientInfoService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_client_info(
    request: Request,
    client_info_data: TalentClientInfoCreate,
    service: service_dependency,
) -> GeneralResponse:
    """Create a new client information record for a talent.
    
    Args:
        request: FastAPI request object
        client_info_data: The client information data to create
        service: Injected client info service
        
    Returns:
        GeneralResponse with created client information data
    """
    try:
        client_info = service.create_client_info(client_info_data)
        
        if not client_info:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create client information. Client information may already exist for this talent."
            )
        
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Client information created successfully",
            data=client_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/talent/{talent_id}", response_model=GeneralResponse)
async def get_client_info_by_talent(
    request: Request,
    talent_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get client information for a specific talent.
    
    Args:
        request: FastAPI request object
        talent_id: The talent profile ID
        service: Injected client info service
        
    Returns:
        GeneralResponse with client information data
    """
    try:
        client_info = service.get_client_info_by_talent_id(talent_id)
        
        if not client_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client information not found for this talent"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information retrieved successfully",
            data=client_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/client/{client_id}", response_model=GeneralResponse)
async def get_client_info_by_client(
    request: Request,
    client_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get client information for a specific client.
    
    Args:
        request: FastAPI request object
        client_id: The client ID
        service: Injected client info service
        
    Returns:
        GeneralResponse with client information data
    """
    try:
        client_info_list = service.get_client_info_by_client_id(client_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information retrieved successfully",
            data=[client_info.model_dump() for client_info in client_info_list]
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_client_info(
    request: Request,
    service: service_dependency,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Number of items per page"),
) -> GeneralResponse:
    """Get all client information with pagination.
    
    Args:
        request: FastAPI request object
        service: Injected client info service
        page: Page number (starts from 1)
        page_size: Number of items per page
        
    Returns:
        GeneralResponse with paginated client information data
    """
    try:
        skip = (page - 1) * page_size
        client_info_list = service.get_all_client_info(skip=skip, limit=page_size)
        # For now, we'll return the count as the length of the list
        # In a real implementation, you might want to add a count method to the service
        total_count = len(client_info_list)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information retrieved successfully",
            data={
                "items": [client_info.model_dump() for client_info in client_info_list],
                "total_count": total_count,
                "page": page,
                "page_size": page_size,
                "total_pages": (total_count + page_size - 1) // page_size
            }
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{client_info_id}", response_model=GeneralResponse)
async def get_client_info_by_id(
    request: Request,
    client_info_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Get a specific client information by ID.
    
    Args:
        request: FastAPI request object
        client_info_id: The client information ID
        service: Injected client info service
        
    Returns:
        GeneralResponse with client information data
    """
    try:
        client_info = service.get_client_info_by_id(client_info_id)
        
        if not client_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client information not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information retrieved successfully",
            data=client_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{client_info_id}", response_model=GeneralResponse)
async def update_client_info(
    request: Request,
    client_info_id: int,
    client_info_data: TalentClientInfoUpdate,
    service: service_dependency,
) -> GeneralResponse:
    """Update an existing client information record.
    
    Args:
        request: FastAPI request object
        client_info_id: The client information ID to update
        client_info_data: The updated client information data
        service: Injected client info service
        
    Returns:
        GeneralResponse with updated client information data
    """
    try:
        client_info = service.update_client_info(client_info_id, client_info_data)
        
        if not client_info:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client information not found or update failed"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information updated successfully",
            data=client_info.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{client_info_id}", response_model=GeneralResponse)
async def delete_client_info(
    request: Request,
    client_info_id: int,
    service: service_dependency,
) -> GeneralResponse:
    """Delete a client information.
    
    Args:
        request: FastAPI request object
        client_info_id: The client information ID to delete
        service: Injected client info service
        
    Returns:
        GeneralResponse confirming deletion
    """
    try:
        deleted = service.delete_client_info(client_info_id)
        
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Client information not found"
            )
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client information deleted successfully",
            data={"deleted": True}
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )