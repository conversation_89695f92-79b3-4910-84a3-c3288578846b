"""API v1 router initialization."""

from .auth_router import router as auth_router
from .profile_router import router as profile_router
from .user_router import router as user_router
from .talent.banking_router import router as banking_router
from .talent.candidate_router import router as candidate_router
from .talent.emergency_contact_router import router as emergency_contact_router
from .talent.payroll_router import router as payroll_router
from .talent.wage_router import router as wage_router
from .talent.chronic_conditions_router import router as chronic_conditions_router
from .talent.ongoing_health_router import router as ongoing_health_router
from .talent.past_health_router import router as past_health_router
from .talent.documents_router import router as documents_router
from .talent.client_info_router import router as talent_client_info_router
from .talent.position_router import router as position_router
from .talent.vacation_router import router as vacation_router
from .talent.location_router import router as location_router
from .talent.equipment_mapping_router import router as equipment_mapping_router
from .talent.software_mapping_router import router as software_mapping_router
from .master.client_info_router import router as client_info_router
from .master.equipment_router import router as equipment_router
from .master.role_router import router as role_router
from .master.module_router import router as module_router
from .master.role_module_permission_mapping_router import router as role_module_permission_mapping_router

__all__ = [
    "auth_router",
    "profile_router",
    "user_router",
    "banking_router",
    "candidate_router",
    "emergency_contact_router",
    "payroll_router",
    "wage_router",
    "chronic_conditions_router",
    "ongoing_health_router",
    "past_health_router",
    "documents_router",
    "talent_client_info_router",
    "position_router",
    "vacation_router",
    "location_router",
    "equipment_mapping_router",
    "software_mapping_router",
    "client_info_router",
    "equipment_router",
    "role_router",
    "module_router",
    "role_module_permission_mapping_router",
]