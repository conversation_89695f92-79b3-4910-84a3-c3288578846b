from typing import Annotated, Dict, Any
from fastapi import APIRouter, status, Response, Depends, HTTPException, Request
from fastapi.security import OAuth2PasswordRequestForm
from app.core.jwt import create_access_token
from app.schemas.login_schema import LoginSchema
from app.services import AuthService
from app.core import log_api_error

router = APIRouter()

service_dependency = Annotated[AuthService, Depends()]


@router.post("/login", status_code=status.HTTP_200_OK, response_model=Dict[str, Any])
async def login(
    request: Request,
    req: LoginSchema,
    service: service_dependency,
    response: Response,
):
    try:
        data = await service.login(req)
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id="anonymous",
        )
        response.status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        data = {
            "message": str(e),
            "status": "fail",
        }
    return data


@router.post("/login-token", status_code=status.HTTP_200_OK, include_in_schema=False)
async def login_for_access_token(
    request: Request,
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()],
    service: service_dependency,
    response: Response,
) -> Dict[str, Any]:
    try:
        # Convert OAuth2 form data to login schema
        req = LoginSchema(email=form_data.username, password=form_data.password)
        user = await service.login(req)
        user_email = user.get("email")
        if not user_email:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User email not found in login response",
            )
        token_data = await create_access_token({"sub": user_email})
        return {
            "access_token": token_data,
            "token_type": "bearer",
            "status": "success",
        }
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=401,
            error=str(e),
            user_id="anonymous",
        )
        response.status_code = status.HTTP_401_UNAUTHORIZED
        return {
            "message": str(e),
            "status": "fail",
        }
