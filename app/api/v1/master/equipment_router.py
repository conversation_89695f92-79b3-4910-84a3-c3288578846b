"""Router for master equipment management.

This module contains the FastAPI router for master equipment endpoints.
"""

from typing import List, Annotated
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from app.response_models import GeneralResponse
from app.services.master.equipment_service import EquipmentService
from app.schemas.master.equipment_schema import (
    MasterEquipmentCreate,
    MasterEquipmentUpdate,
    MasterEquipmentResponse
)
from app.core.logs import log_api_error

router = APIRouter(prefix="/equipment", tags=["Master Equipment"])


@router.post(
    "/",
    response_model=MasterEquipmentResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create new equipment",
    description="Create a new master equipment record"
)
async def create_equipment(
    request: Request,
    equipment_data: MasterEquipmentCreate,
    equipment_service: Annotated[EquipmentService, Depends()]
) -> MasterEquipmentResponse:
    """Create a new master equipment record.
    
    Args:
        equipment_data: Equipment data to create
        equipment_service: Equipment service dependency
        
    Returns:
        Created equipment response
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        return equipment_service.create_equipment(equipment_data)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error creating equipment: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/{equipment_id}",
    response_model=MasterEquipmentResponse,
    summary="Get equipment by ID",
    description="Retrieve a specific equipment record by its ID"
)
async def get_equipment(
    request: Request,
    equipment_id: int,
    equipment_service: Annotated[EquipmentService, Depends()]
) -> MasterEquipmentResponse:
    """Get equipment by ID.
    
    Args:
        equipment_id: Equipment ID
        equipment_service: Equipment service dependency
        
    Returns:
        Equipment response
        
    Raises:
        HTTPException: If equipment not found
    """
    try:
        return equipment_service.get_equipment_by_id(equipment_id)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting equipment: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/",
    response_model=List[MasterEquipmentResponse],
    summary="Get all equipment",
    description="Retrieve all equipment records with pagination"
)
async def get_all_equipment(
    request: Request,
    equipment_service: Annotated[EquipmentService, Depends()],
    skip: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100
) -> List[MasterEquipmentResponse]:
    """Get all equipment records with pagination.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        equipment_service: Equipment service dependency
        
    Returns:
        List of equipment responses
    """
    try:
        return equipment_service.get_all_equipment(skip=skip, limit=limit)
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting equipment list: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put(
    "/{equipment_id}",
    response_model=MasterEquipmentResponse,
    summary="Update equipment",
    description="Update an existing equipment record"
)
async def update_equipment(
    request: Request,
    equipment_id: int,
    equipment_data: MasterEquipmentUpdate,
    equipment_service: Annotated[EquipmentService, Depends()]
) -> MasterEquipmentResponse:
    """Update an existing equipment record.
    
    Args:
        equipment_id: Equipment ID to update
        equipment_data: Updated equipment data
        equipment_service: Equipment service dependency
        
    Returns:
        Updated equipment response
        
    Raises:
        HTTPException: If equipment not found or update fails
    """
    try:
        return equipment_service.update_equipment(equipment_id, equipment_data)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error updating equipment: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete(
    "/{equipment_id}",
    response_model=GeneralResponse,
    summary="Delete equipment",
    description="Delete an equipment record"
)
async def delete_equipment(
    request: Request,
    equipment_id: int,
    equipment_service: Annotated[EquipmentService, Depends()]
) -> GeneralResponse:
    """Delete an equipment record.
    
    Args:
        equipment_id: Equipment ID to delete
        equipment_service: Equipment service dependency
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If equipment not found or deletion fails
    """
    try:
        equipment_service.delete_equipment(equipment_id)
        return GeneralResponse(
            message='Equipment deleted successfully',
            status_code=200,
            data=None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error deleting equipment: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
