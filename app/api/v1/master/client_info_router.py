"""Master Client Info API router."""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, Request, status, Query
from app.services.master.client_info_service import MasterClientInfoService
from app.schemas.master.client_info_schema import (
    MasterClientInfoCreate,
    MasterClientInfoUpdate
)
from app.response_models.general_response import GeneralResponse
from app.core import log_api_error

router = APIRouter()

__service = Annotated[MasterClientInfoService, Depends()]


@router.post("/", response_model=GeneralResponse, status_code=status.HTTP_201_CREATED)
async def create_client(
    request: Request,
    client_data: MasterClientInfoCreate,
    service: __service,
) -> GeneralResponse:
    """Create a new client."""
    try:
        client = service.create_client(client_data)
        return GeneralResponse(
            status_code=status.HTTP_201_CREATED,
            message="Client created successfully",
            data=client.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/", response_model=GeneralResponse)
async def get_all_clients(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all clients with pagination."""
    try:
        clients = service.get_all_clients(skip=skip, limit=limit)
        client_responses = [client.model_dump() for client in clients]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Clients retrieved successfully",
            data=client_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/active", response_model=GeneralResponse)
async def get_active_clients(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all active clients."""
    try:
        clients = service.get_active_clients(skip=skip, limit=limit)
        client_responses = [client.model_dump() for client in clients]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Active clients retrieved successfully",
            data=client_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/inactive", response_model=GeneralResponse)
async def get_inactive_clients(
    request: Request,
    service: __service,
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Get all inactive clients."""
    try:
        clients = service.get_inactive_clients(skip=skip, limit=limit)
        client_responses = [client.model_dump() for client in clients]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Inactive clients retrieved successfully",
            data=client_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/search", response_model=GeneralResponse)
async def search_clients_by_name(
    request: Request,
    service: __service,
    name_pattern: str = Query(..., description="Name pattern to search for"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
) -> GeneralResponse:
    """Search clients by name pattern."""
    try:
        clients = service.search_clients_by_name(name_pattern, skip=skip, limit=limit)
        client_responses = [client.model_dump() for client in clients]
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Clients search completed successfully",
            data=client_responses
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/{client_id}", response_model=GeneralResponse)
async def get_client_by_id(
    request: Request,
    client_id: int,
    service: __service,
) -> GeneralResponse:
    """Get client by ID."""
    try:
        client = service.get_client_by_id(client_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client retrieved successfully",
            data=client.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/name/{name}", response_model=GeneralResponse)
async def get_client_by_name(
    request: Request,
    name: str,
    service: __service,
) -> GeneralResponse:
    """Get client by name."""
    try:
        client = service.get_client_by_name(name)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client retrieved successfully",
            data=client.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.get("/email/{email}", response_model=GeneralResponse)
async def get_client_by_email(
    request: Request,
    email: str,
    service: __service,
) -> GeneralResponse:
    """Get client by email."""
    try:
        client = service.get_client_by_email(email)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client retrieved successfully",
            data=client.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.put("/{client_id}", response_model=GeneralResponse)
async def update_client(
    request: Request,
    client_id: int,
    client_data: MasterClientInfoUpdate,
    service: __service,
) -> GeneralResponse:
    """Update client information."""
    try:
        client = service.update_client(client_id, client_data)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client updated successfully",
            data=client.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "already exists" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.delete("/{client_id}", response_model=GeneralResponse)
async def delete_client(
    request: Request,
    client_id: int,
    service: __service,
) -> GeneralResponse:
    """Delete a client."""
    try:
        service.delete_client(client_id)
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Client deleted successfully",
            data=None
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        elif "administrators" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )


@router.patch("/{client_id}/toggle-status", response_model=GeneralResponse)
async def toggle_client_status(
    request: Request,
    client_id: int,
    service: __service,
) -> GeneralResponse:
    """Toggle client active status."""
    try:
        client = service.toggle_client_status(client_id)
        status_text = "activated" if client.is_active else "deactivated"
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message=f"Client {status_text} successfully",
            data=client.model_dump()
        )
    except ValueError as e:
        error_msg = str(e)
        if "not found" in error_msg.lower():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=error_msg
            )
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_msg
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=500,
            error=str(e),
            user_id=str(service.current_user.id),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error",
        )