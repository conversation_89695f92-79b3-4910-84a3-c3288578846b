"""Router for master role management.

This module contains the FastAPI router for master role endpoints.
"""

from typing import List, Annotated
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from app.response_models import GeneralResponse
from app.services.master.role_service import RoleService
from app.schemas.master.role_schema import (
    MasterRoleCreate,
    MasterRoleUpdate,
    MasterRoleResponse
)
from app.core.logs import log_api_error

router = APIRouter(prefix="/role", tags=["Master Role"])


@router.post(
    "/",
    response_model=MasterRoleResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create new role",
    description="Create a new master role record"
)
async def create_role(
    request: Request,
    role_data: MasterRoleCreate,
    role_service: Annotated[RoleService, Depends()]
) -> MasterRoleResponse:
    """Create a new master role record.
    
    Args:
        role_data: Role data to create
        role_service: Role service dependency
        
    Returns:
        Created role response
        
    Raises:
        HTTPException: If creation fails
    """
    try:
        return role_service.create_role(role_data)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error creating role: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/{role_id}",
    response_model=MasterRoleResponse,
    summary="Get role by ID",
    description="Retrieve a specific role record by its ID"
)
async def get_role(
    request: Request,
    role_id: int,
    role_service: Annotated[RoleService, Depends()]
) -> MasterRoleResponse:
    """Get role by ID.
    
    Args:
        role_id: Role ID
        role_service: Role service dependency
        
    Returns:
        Role response
        
    Raises:
        HTTPException: If role not found
    """
    try:
        return role_service.get_role_by_id(role_id)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting role: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/",
    response_model=List[MasterRoleResponse],
    summary="Get all roles",
    description="Retrieve all role records with pagination"
)
async def get_all_roles(
    request: Request,
    role_service: Annotated[RoleService, Depends()],
    skip: Annotated[int, Query(ge=0)] = 0,
    limit: Annotated[int, Query(ge=1, le=100)] = 100
) -> List[MasterRoleResponse]:
    """Get all role records with pagination.
    
    Args:
        skip: Number of records to skip
        limit: Maximum number of records to return
        role_service: Role service dependency
        
    Returns:
        List of role responses
    """
    try:
        return role_service.get_all_roles(skip=skip, limit=limit)
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error getting role list: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.put(
    "/{role_id}",
    response_model=MasterRoleResponse,
    summary="Update role",
    description="Update an existing role record"
)
async def update_role(
    request: Request,
    role_id: int,
    role_data: MasterRoleUpdate,
    role_service: Annotated[RoleService, Depends()]
) -> MasterRoleResponse:
    """Update an existing role record.
    
    Args:
        role_id: Role ID to update
        role_data: Updated role data
        role_service: Role service dependency
        
    Returns:
        Updated role response
        
    Raises:
        HTTPException: If role not found or update fails
    """
    try:
        return role_service.update_role(role_id, role_data)
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error updating role: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.delete(
    "/{role_id}",
    response_model=GeneralResponse,
    summary="Delete role",
    description="Delete a role record"
)
async def delete_role(
    request: Request,
    role_id: int,
    role_service: Annotated[RoleService, Depends()]
) -> GeneralResponse:
    """Delete a role record.
    
    Args:
        role_id: Role ID to delete
        role_service: Role service dependency
        
    Returns:
        Success message
        
    Raises:
        HTTPException: If role not found or deletion fails
    """
    try:
        role_service.delete_role(role_id)
        return GeneralResponse(
            message='Role deleted successfully',
            status_code=200,
            data=None
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=f"Unexpected error deleting role: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )