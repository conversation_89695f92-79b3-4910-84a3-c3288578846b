"""Router for master module management.

This module contains the FastAPI router for master module endpoints.
"""

from typing import Annotated
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from app.response_models import GeneralResponse
from app.services.master.module_service import ModuleService
from app.schemas.master.module_schema import MasterModuleResponse
from app.core.logs import log_api_error

router = APIRouter(prefix="/module", tags=["Master Module"])


@router.get(
    "/{module_id}",
    response_model=GeneralResponse,
    status_code=status.HTTP_200_OK,
    summary="Get module by ID",
    description="Retrieve a specific module by its ID"
)
async def get_module_by_id(
    module_id: int,
    request: Request,
    service: Annotated[ModuleService, Depends()]
) -> GeneralResponse:
    """Get a module by its ID.
    
    Args:
        module_id: The ID of the module to retrieve
        request: The HTTP request object
        service: The module service dependency
        
    Returns:
        GeneralResponse: Response containing the module data
        
    Raises:
        HTTPException: If module is not found or other errors occur
    """
    try:
        module: MasterModuleResponse = service.get_module_by_id(module_id)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Module retrieved successfully",
            data=module.model_dump()
        )
    except HTTPException:
        raise
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/",
    response_model=GeneralResponse,
    status_code=status.HTTP_200_OK,
    summary="Get all modules",
    description="Retrieve all modules with pagination"
)
async def get_all_modules(
    request: Request,
    service: Annotated[ModuleService, Depends()],
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page")
) -> GeneralResponse:
    """Get all modules with pagination.
    
    Args:
        request: The HTTP request object
        service: The module service dependency
        page: Page number for pagination
        limit: Number of items per page
        
    Returns:
        GeneralResponse: Response containing paginated module data
        
    Raises:
        HTTPException: If an error occurs during retrieval
    """
    try:
        skip = (page - 1) * limit
        modules = service.get_all_modules(skip=skip, limit=limit)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Modules retrieved successfully",
            data=[module.model_dump() for module in modules]
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )


@router.get(
    "/active",
    response_model=GeneralResponse,
    status_code=status.HTTP_200_OK,
    summary="Get active modules",
    description="Retrieve all active modules with pagination"
)
async def get_active_modules(
    request: Request,
    service: Annotated[ModuleService, Depends()],
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Number of items per page")
) -> GeneralResponse:
    """Get all active modules with pagination.
    
    Args:
        request: The HTTP request object
        service: The module service dependency
        page: Page number for pagination
        limit: Number of items per page
        
    Returns:
        GeneralResponse: Response containing paginated active module data
        
    Raises:
        HTTPException: If an error occurs during retrieval
    """
    try:
        skip = (page - 1) * limit
        modules = service.get_active_modules(skip=skip, limit=limit)
        
        return GeneralResponse(
            status_code=status.HTTP_200_OK,
            message="Active modules retrieved successfully",
            data=[module.model_dump() for module in modules]
        )
    except Exception as e:
        log_api_error(
            endpoint=request.url.path,
            method=request.method,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error=str(e),
            user_id=str(service.current_user.id)
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error"
        )
