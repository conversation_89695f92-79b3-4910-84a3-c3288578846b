"""Database initialization module.

This module provides functionality to initialize and setup the database
using SQLModel ORM. It creates the database engine and provides methods
to create all database tables.

Attributes:
    engine: The SQLModel database engine.
"""

from sqlmodel import SQLModel, create_engine

from app.core import settings
from app.db.models import (
    User,
    TalentProfile,
    TalentCronicConditions,
    TalentPastHealthIssues,
    TalentOngoingHealthIssues,
    TalentAllergies,
    TalentDocumentsCollected,
    TalentWageInformation,
    TalentWageHistory,
    MasterClientInfo,
    TalentClientInfo,
    TalentSkillSetMapping,
    TalentPayrollInformationMapping,
    TalentBankingInformationMapping,
    TalentEmergencyContactMapping,
    TalentPositionMapping,
    TalentVacationMapping,
    TalentLocationMapping,
    MasterEquipment,
    TalentEquipmentMapping,
    TalentSoftwareMapping,
    MasterRole,
    MasterModule,
    RoleModulePermissionMapping,
)


# Re-export models for backward compatibility
__all__ = [
    "User",
    "TalentProfile",
    "TalentCronicConditions",
    "TalentPastHealthIssues",
    "TalentOngoingHealthIssues",
    "TalentAllergies",
    "TalentDocumentsCollected",
    "TalentWageInformation",
    "TalentWageHistory",
    "MasterClientInfo",
    "TalentClientInfo",
    "TalentSkillSetMapping",
    "TalentPayrollInformationMapping",
    "TalentBankingInformationMapping",
    "TalentEmergencyContactMapping",
    "TalentPositionMapping",
    "TalentVacationMapping",
    "TalentLocationMapping",
    "MasterEquipment",
    "TalentEquipmentMapping",
    "TalentSoftwareMapping",
    "MasterRole",
    "MasterModule",
    "RoleModulePermissionMapping",
    "engine",
    "init_db",
]


engine = create_engine(
    settings.database_url, echo=False if settings.env_type == "local" else False
)


def init_db():
    """Initialize the database.

    This function creates all database tables defined in SQLModel models.
    """

    SQLModel.metadata.create_all(engine)
