"""Database models module.

This module contains all SQLModel classes for the BPO Admin application.
All models include standardized created_at and updated_at timestamp fields.
"""

from datetime import datetime
from typing import Optional

from sqlalchemy import Column, DateTime, func, JSON
from sqlalchemy.ext.mutable import MutableList
from sqlmodel import BigInteger, Field, SQLModel, Text


class User(SQLModel, table=True):
    """SQLModel class for storing user information in the database.

    Attributes:
        id: Primary key for the user
        name: User's display name
        email: User's email address
        password: User's hashed password
        is_active: Whether the user account is active
        is_superuser: Whether the user has admin privileges
        created_at: Timestamp when user was created
        updated_at: Timestamp when user was last updated
    """

    __tablename__: str = "users"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    name: Optional[str] = Field(max_length=20)
    phone: Optional[str] = Field(max_length=20)
    pic: Optional[str] = Field(default=None, max_length=200)
    email: Optional[str] = Field(max_length=50)
    password: Optional[str] = Field(max_length=100)
    is_active: bool = Field(default=True)
    is_superuser: bool = Field(default=False)
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )


class TalentProfile(SQLModel, table=True):
    """SQLModel class for storing talent profile information in the database.

    Attributes:
        id: Primary key for the talent profile
        created_at: Timestamp when talent profile was created
        updated_at: Timestamp when talent profile was last updated
        talent_id: Foreign key reference to the associated talent
        hire_date: Date when the talent was hired
        status: Whether the talent profile is active
        full_time_employee: Whether the talent is a full-time employee
        other_employee: Additional information about the talent (e.g., part-time, contractor)
        contract_period: Contract period of the talent
        contract_end_date: End date of the talent's contract
        name: Talent's name
        first_name: Talent's first name
        middle_name: Talent's middle name
        last_name: Talent's last name
        email: Talent's email address
        company_email: Talent's company email address
        phone: Talent's contact phone number
        phone2: Talent's alternate contact phone number
        dob: Date of birth of the talent
        address_1: Talent's primary address
        address_2: Talent's secondary address
        city: City where the talent is located
        state: State where the talent is located
        country: Country where the talent is located
        zip_code: Zip code of the talent's location
        pic: Profile picture of the talent
    """

    __tablename__: str = "talent_profiles"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    user_agent: Optional[str] = Field(
        description="user agent", title="user agent", default=None
    )
    ip_address: Optional[str] = Field(
        description="ip address", title="ip address", default=None
    )
    talent_id: str = Field(description="system generated id", title="id")
    hire_date: Optional[datetime] = Field(description="hire date", title="hire date")
    status: bool = Field(description="status", title="status", default=True)
    full_time_employee: bool = Field(
        description="full time employee", title="full time employee", default=True
    )
    other_employee: Optional[str] = Field(
        description="other employee", title="other employee", default=None
    )
    full_name: str = Field(max_length=20)
    first_name: str = Field(max_length=20)
    middle_name: Optional[str] = Field(
        description="middle name", title="middle name", default=None
    )
    last_name: str = Field(max_length=20)
    email: str = Field(max_length=50)
    company_email: Optional[str] = Field(
        description="bpo email", title="bpo email", default=None, max_length=50
    )
    phone: str = Field(max_length=10)
    phone2: Optional[str] = Field(
        description="phone 2", title="phone 2", default=None, max_length=10
    )
    dob: Optional[datetime] = Field(
        description="date of birth", title="date of birth", default=None
    )
    address_1: str = Field(max_length=100)
    address_2: Optional[str] = Field(
        description="address 2", title="address 2", default=None
    )
    city: str = Field(max_length=20)
    state: str = Field(max_length=20)
    country: str = Field(max_length=20)
    zip_code: str = Field(max_length=10)
    pic: Optional[str] = Field(max_length=200)
    is_active: bool = Field(default=True)


class TalentCronicConditions(SQLModel, table=True):
    """SQLModel class for storing talent profile cronic conditions information in the database.

    Attributes:
        id: Primary key for the talent profile cronic conditions
        created_at: Timestamp when talent profile cronic conditions was created
        updated_at: Timestamp when talent profile cronic conditions was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        cronic_condition: Cronic condition of the talent
        medication: Medication of the talent
    """

    __tablename__: str = "talent_cronic_conditions"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    cronic_condition: str = Field(
        default=None, description="cronic condition", title="cronic condition"
    )
    medication: Optional[str] = Field(
        default=None, description="medication", title="medication"
    )


class TalentPastHealthIssues(SQLModel, table=True):
    """SQLModel class for storing talent past health issues information in the database.

    Attributes:
        id: Primary key for the talent past health issues information
        created_at: Timestamp when talent past health issues information was created
        updated_at: Timestamp when talent past health issues information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        past_health_issues: Past health issues of the talent
    """

    __tablename__: str = "talent_past_health_issues"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    past_health_issues: str = Field(
        default=None, description="past health issues", title="past health issues"
    )


class TalentOngoingHealthIssues(SQLModel, table=True):
    """SQLModel class for storing talent ongoing health issues information in the database.

    Attributes:
        id: Primary key for the talent ongoing health issues information
        created_at: Timestamp when talent ongoing health issues information was created
        updated_at: Timestamp when talent ongoing health issues information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        ongoing_health_issues: Ongoing health issues of the talent
    """

    __tablename__: str = "talent_ongoing_health_issues"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    ongoing_health_issues: str = Field(
        default=None, description="ongoing health issues", title="ongoing health issues"
    )
    medication: Optional[str] = Field(
        default=None, description="medication", title="medication"
    )


class TalentAllergies(SQLModel, table=True):
    """SQLModel class for storing talent allergies information in the database.

    Attributes:
        id: Primary key for the talent allergies information
        created_at: Timestamp when talent allergies information was created
        updated_at: Timestamp when talent allergies information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        allergy: Allergies of the talent
        medication: Medication of the talent
    """

    __tablename__: str = "talent_allergies"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    allergy: str = Field(default=None, description="allergy", title="allergy")
    medication: Optional[str] = Field(
        default=None, description="medication", title="medication"
    )


class TalentDocumentsCollected(SQLModel, table=True):
    """SQLModel class for storing talent documents information in the database.

    Attributes:
        id: Primary key for the talent documents information
        created_at: Timestamp when talent documents information was created
        updated_at: Timestamp when talent documents information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        doc_type: Personal Document type
        url: Personal Document Front url

    """

    __tablename__: str = "talent_documents_collected"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    doc_type: str = Field(description="type", title="type", max_length=100)
    url: str = Field(description="url", title="url", max_length=250)


class TalentWageInformation(SQLModel, table=True):
    """SQLModel class for storing talent wage information in the database.

    Attributes:
        id: Primary key for the talent wage information
        created_at: Timestamp when talent wage information was created
        updated_at: Timestamp when talent wage information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        weekly_base_wage: Weekly base wage of the talent
        work_days: Work days of the talent
        schedule: Schedule of the talent
        type_of_employee: Type of employee
    """

    __tablename__: str = "talent_wage_information"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    currency: Optional[str] = Field(description="currency", title="currency")
    weekly_base_wage: Optional[float] = Field(
        description="weekly base wage", title="weekly base wage"
    )
    work_days: Optional[str] = Field(description="work days", title="work days")
    schedule: Optional[str] = Field(description="schedule", title="schedule")
    timezone: Optional[str] = Field(
        description="timezone", title="timezone", default="PST"
    )
    type_of_employee: Optional[str] = Field(
        default=None,
        description="type of employee",
        title="type of employee",
    )


class TalentWageHistory(SQLModel, table=True):
    """SQLModel class for storing talent wage history information in the database.

    Attributes:
        id: Primary key for the talent wage history information
        created_at: Timestamp when talent wage history information was created
        updated_at: Timestamp when talent wage history information was last updated
        wage_information_id: Foreign key reference to the associated talent wage information
        period: Period of the wage history
        start_date: Start date of the wage history
        end_date: End date of the wage history
        amount: Amount of the wage history
        worked_days: Worked days of the wage history
        worked_hours: Worked hours of the wage history
    """

    __tablename__: str = "talent_wage_history"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    wage_information_id: int = Field(
        foreign_key="talent_wage_information.id", ondelete="CASCADE", sa_type=BigInteger
    )
    period: str = Field(description="period", title="period")
    start_date: datetime = Field(description="start date", title="start date")
    end_date: datetime = Field(description="end date", title="end date")
    amount: float = Field(description="amount", title="amount")
    worked_days: int = Field(description="worked days", title="worked days")
    worked_hours: float = Field(description="worked hours", title="worked hours")


class MasterClientInfo(SQLModel, table=True):
    """SQLModel class for storing client information in the database.

    Attributes:
        id: Primary key for the client information
        created_at: Timestamp when client information was created
        updated_at: Timestamp when client information was last updated
        name: Name of the client
        address: Address of the client
        city: City of the client
        state: State of the client
        country: Country of the client
        zip_code: Zip code of the client
        phone: Phone number of the client
        email: Email of the client
        website: Website of the client
        notes: Notes of the client
        is_active: Whether the client is active
    """

    __tablename__: str = "master_client_info"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=20)
    address: Optional[str] = Field(max_length=200)
    city: Optional[str] = Field(max_length=20)
    state: Optional[str] = Field(max_length=20)
    country: Optional[str] = Field(max_length=20)
    zip_code: Optional[str] = Field(max_length=10)
    phone: Optional[str] = Field(max_length=20)
    email: Optional[str] = Field(max_length=50)
    website: Optional[str] = Field(max_length=200)
    notes: Optional[str] = Field(max_length=200)
    is_active: bool = Field(default=True)


class TalentClientInfo(SQLModel, table=True):
    """SQLModel class for storing talent client information in the database.

    Attributes:
        id: Primary key for the talent client information
        created_at: Timestamp when talent client information was created
        updated_at: Timestamp when talent client information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        client: Client of the talent
        bpo_manager: BPO manager of the talent
        client_id: Client id of the talent
        reporting_department: Reporting department of the talent
    """

    __tablename__: str = "talent_client_info"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    campaign: Optional[str] = Field(description="campaign", title="campaign")
    bpo_manager: Optional[str] = Field(description="bpo manager", title="bpo manager")
    client_id: int = Field(
        description="client id",
        title="client id",
        nullable=True,
        foreign_key="master_client_info.id",
        ondelete="CASCADE",
        sa_type=BigInteger,
    )
    reporting_department: Optional[str] = Field(
        description="bpo department", title="bpo department"
    )


class TalentSkillSetMapping(SQLModel, table=True):
    """SQLModel class for storing talent profile skill information in the database.

    Attributes:
        id: Primary key for the talent profile skill
        created_at: Timestamp when talent profile skill was created
        updated_at: Timestamp when talent profile skill was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        skills: Array of skills talent has
        years_of_experience: Years of experience in the skill
        notes_from_interview: Notes from the talent interview
        english_level: English level of the talent(Write, Speak, Type)
    """

    __tablename__: str = "talent_skill_set_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    skills: list[str] = Field(
        description="skills",
        title="skills",
        sa_column=Column(MutableList.as_mutable(JSON)),
    )
    years_of_experience: int = Field(
        description="years of experience", title="years of experience"
    )
    notes_from_interview: Optional[str] = Field(
        description="notes from interview",
        title="notes from interview",
        default=None,
        sa_column=Column(Text),
    )
    english_level: list[str] = Field(
        description="english level",
        title="english level",
        sa_column=Column(MutableList.as_mutable(JSON)),
    )


class TalentPayrollInformationMapping(SQLModel, table=True):
    """SQLModel class for storing talent payroll information in the database.

    Attributes:
        id: Primary key for the talent payroll information
        created_at: Timestamp when talent payroll information was created
        updated_at: Timestamp when talent payroll information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        imms: IMMS number of the talent
        curp: CURP of the talent
        rfc: RFC of the talent
    """

    __tablename__: str = "talent_payroll_information_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    imms: str = Field(max_length=100, nullable=True)
    curp: str = Field(max_length=100, nullable=True)
    rfc: str = Field(max_length=100, nullable=True)


class TalentBankingInformationMapping(SQLModel, table=True):
    """SQLModel class for storing banking information in the database.

    Attributes:
        id: Primary key for the banking information
        created_at: Timestamp when banking information was created
        updated_at: Timestamp when banking information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        bank_name: Name of the bank
        account_number: Account number of the talent
        account_type: Type of the account
        clabe: CLABE of the talent
        swift_code: Swift code of the talent
    """

    __tablename__: str = "talent_banking_information_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    bank_name: str = Field(max_length=100, nullable=True)
    account_number: str = Field(max_length=50, nullable=True)
    account_type: str = Field(max_length=20, nullable=True)
    clabe: str = Field(max_length=100, nullable=True)
    swift_code: Optional[str] = Field(max_length=100, nullable=True)


class TalentEmergencyContactMapping(SQLModel, table=True):
    """SQLModel class for storing emergency contact information in the database.

    Attributes:
        id: Primary key for the emergency contact information
        created_at: Timestamp when emergency contact information was created
        updated_at: Timestamp when emergency contact information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        name: Name of the emergency contact
        relationship: Relationship of the emergency contact
        phone: Phone number of the emergency contact
        notes: Note about the emergency contact
    """

    __tablename__: str = "talent_emergency_contact_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    name: str = Field(max_length=20, nullable=True)
    relationship: str = Field(max_length=50, nullable=True)
    phone: str = Field(max_length=20, nullable=True)
    notes: Optional[str] = Field(max_length=200, nullable=True)


class TalentPositionMapping(SQLModel, table=True):
    """SQLModel class for storing talent position information in the database.

    Attributes:
        id: Primary key for the talent position information
        created_at: Timestamp when talent position information was created
        updated_at: Timestamp when talent position information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        position: Position of the talent
        contract_period: Contract period of the talent (Optional)
        contract_end_date: Contract end date of the talent (Optional)
        role: Role of the talent
        title: Title of the talent position
        client_placed: Client placed of the talent
        time: Working hours of the talent
        start_date: Start date of the talent position
        end_date: End date of the talent position
        termination_date: Termination date of the talent
        reason_for_termination: Reason for termination of the talent
        notes_for_termination: Notes for termination of the talent
        salary: Salary of the talent (Optional)
        hours: Working hours of the talent (Optional)
        hourly_rate: Hourly rate of the talent (Optional)
    """

    __tablename__: str = "talent_position_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    position: str = Field(max_length=20, nullable=True)
    contract_period: Optional[str] = Field(max_length=20, nullable=True)
    contract_end_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    role: str = Field(max_length=20, nullable=True)
    title: str = Field(max_length=20, nullable=True)
    client_placed: int = Field(
        description="client placed",
        title="client placed",
        nullable=True,
        foreign_key="master_client_info.id",
        ondelete="CASCADE",
        sa_type=BigInteger,
    )
    time: Optional[str] = Field(max_length=20, nullable=True)
    start_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    end_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    termination_date: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, nullable=True)
    )
    reason_for_termination: str = Field(max_length=20, nullable=True)
    notes_for_termination: str = Field(max_length=20, nullable=True)
    salary: str = Field(max_length=20, nullable=True)
    hours: str = Field(max_length=20, nullable=True)
    hourly_rate: str = Field(max_length=20, nullable=True)


class TalentVacationMapping(SQLModel, table=True):
    """SQLModel class for storing talent vacation information in the database.

    Attributes:
        id: Primary key for the vacation information
        created_at: Timestamp when vacation information was created
        updated_at: Timestamp when vacation information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        year_number: Year number since start date
        available_bereavement_days: Available bereavement days for the year
        remaining_bereavement_days: Remaining bereavement days for the year
        available_vacation_days: Available vacation days for the year
        remaining_vacation_days: Remaining vacation days for the year
        available_parental_days: Available paid days for the year
        remaining_parental_days: Remaining paid days for the year
    """

    __tablename__: str = "talent_vacation_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    year_number: int = Field(description="year number", title="year number")
    accrued_paid_time_off_days: float = Field(
        description="accrued paid time", title="accrued paid time"
    )
    used_paid_time_off_days: float = Field(
        description="used paid time", title="used paid time"
    )
    available_paid_time_off_days: float = Field(
        description="available paid time off days", title="available paid time off days"
    )
    available_bereavement_days: float = Field(
        description="available bereavement days", title="available bereavement days"
    )
    remaining_bereavement_days: float = Field(
        description="remaining bereavement days", title="remaining bereavement days"
    )
    available_vacation_days: float = Field(
        description="available vacation days", title="available vacation days"
    )
    remaining_vacation_days: float = Field(
        description="remaining vacation days", title="remaining vacation days"
    )
    available_parental_days: float = Field(
        description="available parental days", title="available parental days"
    )
    remaining_parental_days: float = Field(
        description="remaining parental days", title="remaining parental days"
    )


class TalentLocationMapping(SQLModel, table=True):
    """SQLModel class for storing talent location mapping information in the database.

    Attributes:
        id: Primary key for the location information
        created_at: Timestamp when location information was created
        updated_at: Timestamp when location information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        location: Location information
        location_id: Location id
    """

    __tablename__: str = "talent_location_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    location: str = Field(max_length=20)
    duration_of_stay: str = Field(max_length=20)


class MasterEquipment(SQLModel, table=True):
    """SQLModel class for storing equipment information in the database.

    Attributes:
        id: Primary key for the equipment information
        created_at: Timestamp when equipment information was created
        updated_at: Timestamp when equipment information was last updated
        name: Equipment name
        description: Equipment description
        location_id: Location id
    """

    __tablename__: str = "master_equipment"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=200)
    serial_number: Optional[str] = Field(max_length=200)
    model: Optional[str] = Field(max_length=200)
    manufacturer: Optional[str] = Field(max_length=200)
    model_number: Optional[str] = Field(max_length=200)
    desktop_name: Optional[str] = Field(max_length=200)
    anydesk_id: Optional[str] = Field(max_length=200)


class TalentEquipmentMapping(SQLModel, table=True):
    """SQLModel class for storing talent equipment mapping information in the database.

    Attributes:
        id: Primary key for the equipment information
        created_at: Timestamp when equipment information was created
        updated_at: Timestamp when equipment information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        equipment: Equipment information
        equipment_id: Equipment id
    """

    __tablename__: str = "talent_equipment_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    equipment_id: int = Field(
        foreign_key="master_equipment.id",
        ondelete="CASCADE",
        sa_type=BigInteger,
        nullable=True,
    )
    duration_of_use: str = Field(max_length=20)
    is_replaced: bool = Field(default=False)


class TalentSoftwareMapping(SQLModel, table=True):
    """SQLModel class for storing talent software mapping information in the database.

    Attributes:
        id: Primary key for the software information
        created_at: Timestamp when software information was created
        updated_at: Timestamp when software information was last updated
        talent_profile_id: Foreign key reference to the associated talent profile
        software: Software information
        software_id: Software id
        software_version: Software version
        software_key: Software key
    """

    __tablename__: str = "talent_software_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    talent_profile_id: int = Field(
        foreign_key="talent_profiles.id", ondelete="CASCADE", sa_type=BigInteger
    )
    software: str = Field(max_length=20)
    software_version: Optional[str] = Field(max_length=20, nullable=True)
    software_key: Optional[str] = Field(max_length=20, nullable=True)


class MasterRole(SQLModel, table=True):
    """SQLModel class for storing role information in the database.

    Attributes:
        id: Primary key for the role information
        created_at: Timestamp when role information was created
        updated_at: Timestamp when role information was last updated
        name: Role name
    """

    __tablename__: str = "master_role"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=20)


class MasterModule(SQLModel, table=True):
    """SQLModel class for storing module information in the database.

    Attributes:
        id: Primary key for the module information
        created_at: Timestamp when module information was created
        updated_at: Timestamp when module information was last updated
        name: Module name
        is_active: Module status
    """

    __tablename__: str = "master_module"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    name: str = Field(max_length=50, nullable=True)
    is_active: bool = Field(default=True)


class RoleModulePermissionMapping(SQLModel, table=True):
    """SQLModel class for storing role permission mapping information in the database.

    Attributes:
        id: Primary key for the role permission mapping information
        created_at: Timestamp when role permission mapping information was created
        updated_at: Timestamp when role permission mapping information was last updated
        role_id: Foreign key reference to the associated role
        permission_id: Foreign key reference to the associated permission
    """

    __tablename__: str = "role_permission_mapping"  # type: ignore

    id: Optional[int] = Field(
        default=None, description="id", title="id", sa_type=BigInteger, primary_key=True
    )
    created_at: Optional[datetime] = Field(
        default=None, sa_column=Column(DateTime, default=func.now(), nullable=True)
    )
    updated_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(
            DateTime, default=func.now(), onupdate=func.now(), nullable=True
        ),
    )
    role_id: int = Field(
        foreign_key="master_role.id", ondelete="CASCADE", sa_type=BigInteger
    )
    module_id: int = Field(
        foreign_key="master_module.id", ondelete="CASCADE", sa_type=BigInteger
    )
    can_list: bool = Field(default=False)
    can_create: bool = Field(default=False)
    can_update: bool = Field(default=False)
    can_delete: bool = Field(default=False)
