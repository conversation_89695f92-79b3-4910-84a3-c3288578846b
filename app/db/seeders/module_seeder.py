from app.db.init_db import engine
from sqlmodel import Session, select

from app.db.models import MasterModule


def seed_modules():
    modules = [
        "Talent Profile",
        "Talent Cronic Conditions",
        "Talent Allergies",
        "Talent Past Health Issues",
        "Talent Ongoing Health Issues",
        "Talent Documents Collected",
        "Talent Wage Information",
        "Master Client",
        "Talent Client Info",
        "Talent Skill Set Mapping",
        "Talent Payroll Information Mapping",
        "Talent Banking Information Mapping",
        "Talent Emergency Contact Mapping",
        "Talent Position Mapping",
        "Talent Vacation Mapping",
        "Talent Location Mapping",
        "Master Equipment",
        "Talent Equipment Mapping",
        "Talent Software Mapping",
        "Master Role",
        "Master Module",
        "Master User",
    ]
    with Session(engine) as session:
        for module in modules:
            statement = session.exec(
                select(MasterModule).where(MasterModule.name == module)
            ).first()

            if not statement:
                module = MasterModule(name=module)
                session.add(module)
        session.commit()
