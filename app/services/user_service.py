"""User service for user management business logic."""

from typing import Annotated, List, Optional, Dict, Any
from fastapi import Depends
from app.core import get_hashed_password
from app.db import User
from app.repositories import UserRepository
from app.schemas.user import UserCreate, UserUpdate, UserResponse
from app.core.jwt import get_current_user


class UserService:
    def __init__(
        self,
        user_repository: Annotated[UserRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.user_repository = user_repository
        self.current_user = current_user
    
    def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create a new user.
        
        Args:
            user_data: User creation data
            
        Returns:
            UserResponse: Created user data
            
        Raises:
            ValueError: If user creation fails or email already exists
        """
        # Check if user with email already exists
        existing_user: Optional[User] = self.user_repository.get_by_email(user_data.email)
        if existing_user:
            raise ValueError("User with this email already exists")
        
        # Hash the password
        hashed_password = get_hashed_password(user_data.password)
        
        # Create user with hashed password
        user_create_dict: Dict[str, Any] = {
            "name": user_data.name,
            "email": user_data.email,
            "phone": user_data.phone,
            "password_hash": hashed_password,
            "is_active": True,
            "is_superuser": False
        }
        
        created_user: User = self.user_repository.create(user_create_dict)
        
        return UserResponse(
            id=created_user.id or 0,
            name=created_user.name or "",
            email=created_user.email or "",
            phone=created_user.phone or "",
            pic=created_user.pic,
            is_active=created_user.is_active or False,
            is_superuser=created_user.is_superuser or False
        )
    
    def get_user_by_id(self, user_id: int) -> UserResponse:
        """Get a user by ID.
        
        Args:
            user_id: User ID to retrieve
            
        Returns:
            UserResponse: User data
            
        Raises:
            ValueError: If user not found
        """
        user: User = self.user_repository.get_by_id(user_id)
        
        return UserResponse(
            id=user.id or 0,
            name=user.name or "",
            email=user.email or "",
            phone=user.phone or "",
            pic=user.pic,
            is_active=user.is_active or False,
            is_superuser=user.is_superuser or False
        )
    
    def get_user_by_email(self, email: str) -> UserResponse:
        """Get a user by email.
        
        Args:
            email: User email to retrieve
            
        Returns:
            UserResponse: User data
            
        Raises:
            ValueError: If user not found
        """
        user: Optional[User] = self.user_repository.get_by_email(email)
        if not user:
            raise ValueError("User not found")
        
        return UserResponse(
            id=user.id or 0,
            name=user.name or "",
            email=user.email or "",
            phone=user.phone or "",
            pic=user.pic,
            is_active=user.is_active or False,
            is_superuser=user.is_superuser or False
        )
    
    def get_all_users(self) -> List[UserResponse]:
        """Get all users (admin only).
        
        Returns:
            List[UserResponse]: List of all users
            
        Raises:
            ValueError: If user is not admin
        """
        # Check if current user is admin
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can view all users")
        
        users: List[User] = self.user_repository.get_all()
        
        return [
            UserResponse(
                id=user.id or 0,
                name=user.name or "",
                email=user.email or "",
                phone=user.phone or "",
                pic=user.pic,
                is_active=user.is_active or False,
                is_superuser=user.is_superuser or False
            )
            for user in users
        ]
    
    def update_user(self, user_id: int, user_data: UserUpdate) -> UserResponse:
        """Update a user.
        
        Args:
            user_id: User ID to update
            user_data: Updated user data
            
        Returns:
            UserResponse: Updated user data
            
        Raises:
            ValueError: If user not found or permission denied
        """
        # Check if user can update (self or admin)
        if user_id != self.current_user.id and not self.current_user.is_superuser:
            raise ValueError("You can only update your own profile or be an administrator")
        
        user: User = self.user_repository.get_by_id(user_id)
        update_dict: Dict[str, Any] = {k: v for k, v in user_data.model_dump().items() if v is not None}
        updated_user: User = self.user_repository.update(user, update_dict)
        
        return UserResponse(
            id=updated_user.id or 0,
            name=updated_user.name or "",
            email=updated_user.email or "",
            phone=updated_user.phone or "",
            pic=updated_user.pic,
            is_active=updated_user.is_active or False,
            is_superuser=updated_user.is_superuser or False
        )
    
    def delete_user(self, user_id: int) -> bool:
        """Delete a user (admin only).
        
        Args:
            user_id: User ID to delete
            
        Returns:
            bool: True if deletion successful
            
        Raises:
            ValueError: If user not found, not admin, or deletion fails
        """
        # Check if current user is admin
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can delete users")
        
        # Prevent self-deletion
        if user_id == self.current_user.id:
            raise ValueError("You cannot delete your own account")
        
        user: User = self.user_repository.get_by_id(user_id)
        self.user_repository.delete(user)
        
        return True
    
    def toggle_user_status(self, user_id: int) -> UserResponse:
        """Toggle user active status (admin only).
        
        Args:
            user_id: User ID to toggle status
            
        Returns:
            UserResponse: Updated user data
            
        Raises:
            ValueError: If user not found, not admin, or operation fails
        """
        # Check if current user is admin
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can toggle user status")
        
        # Prevent self-deactivation
        if user_id == self.current_user.id:
            raise ValueError("You cannot deactivate your own account")
        
        user: User = self.user_repository.get_by_id(user_id)
        updated_user: User = self.user_repository.toggle_status(user)
        
        return UserResponse(
            id=updated_user.id or 0,
            name=updated_user.name or "",
            email=updated_user.email or "",
            phone=updated_user.phone or "",
            pic=updated_user.pic,
            is_active=updated_user.is_active or False,
            is_superuser=updated_user.is_superuser or False
        )
