"""Service layer for managing talent ongoing health issues."""

from typing import Annotated, List

from fastapi import Depends, HTTPException, status

from app.core.jwt import get_current_user
from app.db import TalentOngoingHealthIssues, User
from app.repositories.talent import TalentOngoingHealthRepository


class TalentOngoingHealthService:
    """Service class for talent ongoing health issues business logic."""
    
    def __init__(
        self,
        repository: Annotated[TalentOngoingHealthRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            repository: The repository for ongoing health issues
            current_user: Currently authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def create_ongoing_health_issue(
        self, ongoing_health_issue: TalentOngoingHealthIssues
    ) -> TalentOngoingHealthIssues:
        """Create a new ongoing health issue.
        
        Args:
            ongoing_health_issue: The ongoing health issue data to create
            
        Returns:
            The created ongoing health issue
        """
        return self.repository.create_ongoing_health_issue(ongoing_health_issue)

    def get_ongoing_health_issues_by_talent_id(
        self, talent_id: int
    ) -> List[TalentOngoingHealthIssues]:
        """Get all ongoing health issues for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of ongoing health issues
        """
        return self.repository.get_ongoing_health_issues_by_talent_id(talent_id)

    def get_ongoing_health_issue_by_id(
        self, issue_id: int
    ) -> TalentOngoingHealthIssues:
        """Get an ongoing health issue by ID.
        
        Args:
            issue_id: The ongoing health issue ID
            
        Returns:
            The ongoing health issue
            
        Raises:
            HTTPException: If ongoing health issue not found
        """
        issue = self.repository.get_ongoing_health_issue_by_id(issue_id)
        if not issue:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Ongoing health issue not found"
            )
        return issue

    def update_ongoing_health_issue(
        self, ongoing_health_issue: TalentOngoingHealthIssues
    ) -> TalentOngoingHealthIssues:
        """Update an existing ongoing health issue.
        
        Args:
            ongoing_health_issue: The updated ongoing health issue data
            
        Returns:
            The updated ongoing health issue
        """
        return self.repository.update_ongoing_health_issue(ongoing_health_issue)

    def delete_ongoing_health_issue(self, issue_id: int) -> bool:
        """Delete an ongoing health issue.
        
        Args:
            issue_id: The ongoing health issue ID to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            HTTPException: If ongoing health issue not found
        """
        deleted = self.repository.delete_ongoing_health_issue(issue_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Ongoing health issue not found"
            )
        return deleted
