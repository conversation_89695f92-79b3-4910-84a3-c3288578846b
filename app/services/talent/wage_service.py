"""Service for talent wage information management."""

from typing import Annotated, List, Optional

from fastapi import Depends, HTTPException, status

from app.core import log_database_error
from app.core.jwt import get_current_user
from app.db import TalentWageInformation, TalentWageHistory, User
from app.repositories.talent import WageRepository


class WageService:
    """Service class for wage information business logic."""  
    def __init__(
        self,
        repository: Annotated[WageRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            session: Database session
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def get_wage_info_by_talent_id(self, talent_id: int) -> Optional[TalentWageInformation]:
        """Get wage information by talent profile ID.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            TalentWageInformation object or None if not found
            
        Raises:
            HTTPException: If database error occurs
        """
        try:
            return self.repository.get_wage_info_by_talent_id(talent_id)
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="talent_wage_information",
                error=e,
                additional_context={"talent_profile_id": talent_id}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve wage information"
            )

    def get_wage_info_by_id(self, wage_id: int) -> TalentWageInformation:
        """Get wage information by ID.
        
        Args:
            wage_id: The wage information ID
            
        Returns:
            TalentWageInformation object
            
        Raises:
            HTTPException: If not found or database error occurs
        """
        try:
            wage_info = self.repository.get_wage_info_by_id(wage_id)
            if not wage_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Wage information not found"
                )
            return wage_info
        except HTTPException:
            raise
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="talent_wage_information",
                error=e,
                additional_context={"wage_id": wage_id}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve wage information"
            )

    def create_wage_info(self, wage_info: TalentWageInformation) -> TalentWageInformation:
        """Create a new wage information record.
        
        Args:
            wage_info: The wage information to create
            
        Returns:
            Created TalentWageInformation object
            
        Raises:
            HTTPException: If database error occurs
        """
        try:
            return self.repository.create_wage_info(wage_info)
        except Exception as e:
            log_database_error(
                operation="INSERT",
                table="talent_wage_information",
                error=e
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create wage information"
            )

    def update_wage_info(self, wage_info: TalentWageInformation) -> TalentWageInformation:
        """Update an existing wage information record.
        
        Args:
            wage_info: The wage information to update
            
        Returns:
            Updated TalentWageInformation object
            
        Raises:
            HTTPException: If not found or database error occurs
        """
        try:
            # Check if wage info exists
            if wage_info.id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Wage information ID is required"
                )
            existing_wage_info = self.repository.get_wage_info_by_id(wage_info.id)
            if not existing_wage_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Wage information not found"
                )
            return self.repository.update_wage_info(wage_info)
        except HTTPException:
            raise
        except Exception as e:
            log_database_error(
                operation="UPDATE",
                table="talent_wage_information",
                error=e
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update wage information"
            )

    def delete_wage_info(self, wage_id: int) -> None:
        """Delete a wage information record.
        
        Args:
            wage_id: The wage information ID to delete
            
        Raises:
            HTTPException: If not found or database error occurs
        """
        try:
            # Check if wage info exists
            existing_wage_info = self.repository.get_wage_info_by_id(wage_id)
            if not existing_wage_info:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Wage information not found"
                )
            
            success = self.repository.delete_wage_info(wage_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete wage information"
                )
        except HTTPException:
            raise
        except Exception as e:
            log_database_error(
                operation="DELETE",
                table="talent_wage_information",
                error=e
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete wage information"
            )

    def get_wage_history_by_wage_id(self, wage_id: int) -> List[TalentWageHistory]:
        """Get wage history by wage information ID.
        
        Args:
            wage_id: The wage information ID
            
        Returns:
            List of TalentWageHistory objects
            
        Raises:
            HTTPException: If database error occurs
        """
        try:
            return self.repository.get_wage_history_by_wage_id(wage_id)
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="talent_wage_history",
                error=e,
                additional_context={"wage_id": wage_id}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve wage history"
            )

    def get_wage_history_by_id(self, history_id: int) -> TalentWageHistory:
        """Get wage history by ID.
        
        Args:
            history_id: The wage history ID
            
        Returns:
            TalentWageHistory object
            
        Raises:
            HTTPException: If not found or database error occurs
        """
        try:
            wage_history = self.repository.get_wage_history_by_id(history_id)
            if not wage_history:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Wage history not found"
                )
            return wage_history
        except HTTPException:
            raise
        except Exception as e:
            log_database_error(
                operation="SELECT",
                table="talent_wage_history",
                error=e,
                additional_context={"history_id": history_id}
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve wage history"
            )

    def create_wage_history(self, wage_history: TalentWageHistory) -> TalentWageHistory:
        """Create a new wage history record.
        
        Args:
            wage_history: The wage history to create
            
        Returns:
            Created TalentWageHistory object
            
        Raises:
            HTTPException: If database error occurs
        """
        try:
            return self.repository.create_wage_history(wage_history)
        except Exception as e:
            log_database_error(
                operation="INSERT",
                table="talent_wage_history",
                error=e
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create wage history"
            )

    def update_wage_history(self, wage_history: TalentWageHistory) -> TalentWageHistory:
        """Update an existing wage history record.
        
        Args:
            wage_history: The wage history to update
            
        Returns:
            Updated TalentWageHistory object
            
        Raises:
            HTTPException: If not found or database error occurs
        """
        try:
            # Check if wage history exists
            if wage_history.id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Wage history ID is required"
                )
            existing_wage_history = self.repository.get_wage_history_by_id(wage_history.id)
            if not existing_wage_history:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Wage history not found"
                )
            return self.repository.update_wage_history(wage_history)
        except HTTPException:
            raise
        except Exception as e:
            log_database_error(
                operation="UPDATE",
                table="talent_wage_history",
                error=e
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update wage history"
            )

    def delete_wage_history(self, history_id: int) -> None:
        """Delete a wage history record.
        
        Args:
            history_id: The wage history ID to delete
            
        Raises:
            HTTPException: If not found or database error occurs
        """
        try:
            # Check if wage history exists
            existing_wage_history = self.repository.get_wage_history_by_id(history_id)
            if not existing_wage_history:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Wage history not found"
                )
            
            success = self.repository.delete_wage_history(history_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete wage history"
                )
        except HTTPException:
            raise
        except Exception as e:
            log_database_error(
                operation="DELETE",
                table="talent_wage_history",
                error=e
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete wage history"
            )
