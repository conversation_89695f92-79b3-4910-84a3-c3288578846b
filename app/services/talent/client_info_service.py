"""Client information service for talent management.

This module contains the service class for talent client information business logic.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.client_info_repository import TalentClientInfoRepository
from app.schemas.talent.client_info_schema import (
    TalentClientInfoCreate,
    TalentClientInfoUpdate,
    TalentClientInfoResponse
)
from app.core.logs import log_error_with_context


class TalentClientInfoService:
    """Service class for talent client information business logic.
    
    Handles all business operations related to talent client information.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentClientInfoRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_client_info(
        self, 
        client_info_data: TalentClientInfoCreate
    ) -> Optional[TalentClientInfoResponse]:
        """Create new client information for a talent.
        
        Args:
            client_info_data: Client information data to create
            
        Returns:
            Created client information response or None if creation fails
        """
        try:
            # Check if client information already exists for this talent
            if self.repository.exists_by_talent_id(client_info_data.talent_profile_id):
                log_error_with_context(
                    error=ValueError("Client information already exists for this talent"),
                    context={
                        "talent_profile_id": client_info_data.talent_profile_id,
                        "operation": "create_client_info"
                    }
                )
                return None
            
            db_client_info = self.repository.create(client_info_data)
            if db_client_info:
                return TalentClientInfoResponse.model_validate(db_client_info)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_client_info",
                    "talent_profile_id": client_info_data.talent_profile_id
                }
            )
            raise e
    
    def get_client_info_by_id(self, client_info_id: int) -> Optional[TalentClientInfoResponse]:
        """Get client information by ID.
        
        Args:
            client_info_id: Client information ID
            
        Returns:
            Client information response or None if not found
        """
        try:
            db_client_info = self.repository.get_by_id(client_info_id)
            if db_client_info:
                return TalentClientInfoResponse.model_validate(db_client_info)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_info_by_id",
                    "client_info_id": client_info_id
                }
            )
            raise e
    
    def get_client_info_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> Optional[TalentClientInfoResponse]:
        """Get client information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Client information response or None if not found
        """
        try:
            db_client_info = self.repository.get_by_talent_id(talent_profile_id)
            if db_client_info:
                return TalentClientInfoResponse.model_validate(db_client_info)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_info_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def get_client_info_by_client_id(
        self, 
        client_id: int
    ) -> List[TalentClientInfoResponse]:
        """Get all talent client information for a specific client.
        
        Args:
            client_id: Client ID
            
        Returns:
            List of client information responses
        """
        try:
            db_client_info_list = self.repository.get_by_client_id(client_id)
            return [
                TalentClientInfoResponse.model_validate(db_client_info)
                for db_client_info in db_client_info_list
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_client_info_by_client_id",
                    "client_id": client_id
                }
            )
            raise e
    
    def get_all_client_info(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[TalentClientInfoResponse]:
        """Get all talent client information with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of client information responses
        """
        try:
            db_client_info_list = self.repository.get_all(skip=skip, limit=limit)
            return [
                TalentClientInfoResponse.model_validate(db_client_info)
                for db_client_info in db_client_info_list
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_client_info",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def update_client_info(
        self, 
        client_info_id: int, 
        client_info_data: TalentClientInfoUpdate
    ) -> Optional[TalentClientInfoResponse]:
        """Update client information.
        
        Args:
            client_info_id: Client information ID
            client_info_data: Updated client information data
            
        Returns:
            Updated client information response or None if update fails
        """
        try:
            # Check if client information exists
            existing_client_info = self.repository.get_by_id(client_info_id)

            if not existing_client_info:
                log_error_with_context(
                    error=ValueError("Client information not found"),
                    context={
                        "client_info_id": client_info_id,
                        "operation": "update_client_info"
                    }
                )
                raise ValueError("Client information not found")

            # Update fields
            update_data = client_info_data.model_dump()
            for field, value in update_data.items():
                if hasattr(existing_client_info, field):
                    setattr(existing_client_info, field, value)
            
            db_client_info = self.repository.update(client_info_id, existing_client_info)
            if db_client_info:
                return TalentClientInfoResponse.model_validate(db_client_info)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_client_info",
                    "client_info_id": client_info_id
                }
            )
            raise e
    
    def delete_client_info(self, client_info_id: int) -> bool:
        """Delete client information.
        
        Args:
            client_info_id: Client information ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Check if client information exists
            existing_client_info = self.repository.get_by_id(client_info_id)
            if not existing_client_info:
                log_error_with_context(
                    error=ValueError("Client information not found"),
                    context={
                        "client_info_id": client_info_id,
                        "operation": "delete_client_info"
                    }
                )
                return False
            
            return self.repository.delete(client_info_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_client_info",
                    "client_info_id": client_info_id
                }
            )
            raise e
    
    def validate_client_info(self, client_info_data: TalentClientInfoCreate) -> List[str]:
        """Validate client information data.
        
        Args:
            client_info_data: Client information data to validate
            
        Returns:
            List of validation error messages
        """
        errors: List[str] = []
        
        try:
            # Validate required fields
            if not client_info_data.talent_profile_id:
                errors.append("Talent profile ID is required")
            
            if not client_info_data.client_id:
                errors.append("Client ID is required")
            
            # Validate campaign if provided
            if client_info_data.campaign and len(client_info_data.campaign.strip()) == 0:
                errors.append("Campaign cannot be empty if provided")
            
            # Validate BPO manager if provided
            if client_info_data.bpo_manager and len(client_info_data.bpo_manager.strip()) == 0:
                errors.append("BPO manager cannot be empty if provided")
            
            # Validate reporting department if provided
            if client_info_data.reporting_department and len(client_info_data.reporting_department.strip()) == 0:
                errors.append("Reporting department cannot be empty if provided")
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "validate_client_info",
                    "talent_profile_id": client_info_data.talent_profile_id
                }
            )
            errors.append("Validation error occurred")
        
        return errors
