"""Service layer for talent profile management."""

from typing import Annotated, List

from fastapi import Depends, HTTPException, status

from app.core.jwt import get_current_user
from app.db import TalentProfile, User
from app.repositories.talent import TalentRepository, TalentChronicConditionsRepository
from app.schemas.talent.talent_profile_schema import ProfileCreate, ProfileUpdate



class TalentService:
    """Service class for talent profile business logic."""
    
    def __init__(
        self,
        talent_repository: Annotated[TalentRepository, Depends()],
        chronic_conditions_repository: Annotated[TalentChronicConditionsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the talent service.
        
        Args:
            db: Database session
            current_user: Current authenticated user
        """
        self.talent_repository = talent_repository
        self.chronic_conditions_repository = chronic_conditions_repository
        self.current_user = current_user

    def get_talent_by_id(self, talent_id: int) -> TalentProfile:
        """Get a talent profile by ID.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            The talent profile
            
        Raises:
            HTTPException: If talent profile not found
        """
        talent = self.talent_repository.get_talent_by_id(talent_id)
        if not talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return talent
    
    def get_all_talents(self) -> List[TalentProfile]:
        """Get all talent profiles.
        
        Returns:
            List of all talent profiles
        """
        return self.talent_repository.get_all_talents()

    def create_talent_profile(self, talent_profile: TalentProfile) -> TalentProfile:
        """Create a new talent profile.
        
        Args:
            talent_profile: The talent profile data to create
            
        Returns:
            The created talent profile
        """
        return self.talent_repository.create_talent_profile(talent_profile)
    
    def update_talent_profile(self, talent_profile: TalentProfile) -> TalentProfile:
        """Update an existing talent profile.
        
        Args:
            talent_profile: The talent profile data to update
            
        Returns:
            The updated talent profile
            
        Raises:
            HTTPException: If talent profile not found
        """
        # Verify the talent exists
        if not talent_profile.id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Talent profile ID is required for updates"
            )

        existing_talent = self.talent_repository.get_talent_by_id(talent_profile.id)
        
        if not existing_talent:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
        return self.talent_repository.update_talent_profile(talent_profile)
    
    def delete_talent_profile(self, talent_id: int) -> None:
        """Delete a talent profile by ID.
        
        Args:
            talent_id: The talent profile ID to delete
            
        Raises:
            HTTPException: If talent profile not found
        """
        if not self.talent_repository.delete_talent_profile(talent_id):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Talent profile not found"
            )
    
    def create_talent_profile_from_schema(self, talent_profile: ProfileCreate) -> TalentProfile:
        """Create a new talent profile from schema.
        
        Args:
            talent_profile: The talent profile data to create
            
        Returns:
            The created talent profile
        """
        # Convert Pydantic schema to SQLModel
        talent_model = TalentProfile(**talent_profile.model_dump())
        return self.create_talent_profile(talent_model)
    
    def update_talent_profile_from_schema(self, talent_id: int, talent_profile: ProfileUpdate) -> TalentProfile:
        """Update an existing talent profile from schema.
        
        Args:
            talent_id: The talent profile ID to update
            talent_profile: The updated talent profile data
            
        Returns:
            The updated talent profile
        """
        # Convert Pydantic schema to SQLModel and set the ID
        talent_data = talent_profile.model_dump()
        talent_model = TalentProfile(id=talent_id, **talent_data)
        return self.update_talent_profile(talent_model)
