"""Service layer for managing talent past health issues."""

from typing import Annotated, List

from fastapi import Depends, HTTPException, status

from app.core.jwt import get_current_user
from app.db import TalentPastHealthIssues, User
from app.repositories.talent import TalentPastHealthRepository


class TalentPastHealthService:
    """Service class for talent past health issues business logic."""
    
    def __init__(
        self,
        repository: Annotated[TalentPastHealthRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            repository: Injected repository
            current_user: Currently authenticated user
        """
        self.repository = repository
        self.current_user = current_user

    def create_past_health_issue(
        self, past_health_issue: TalentPastHealthIssues
    ) -> TalentPastHealthIssues:
        """Create a new past health issue.
        
        Args:
            past_health_issue: The past health issue data to create
            
        Returns:
            The created past health issue
        """
        return self.repository.create_past_health_issue(past_health_issue)

    def get_past_health_issues_by_talent_id(
        self, talent_id: int
    ) -> List[TalentPastHealthIssues]:
        """Get all past health issues for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of past health issues
        """
        return self.repository.get_past_health_issues_by_talent_id(talent_id)

    def get_past_health_issue_by_id(
        self, issue_id: int
    ) -> TalentPastHealthIssues:
        """Get a past health issue by ID.
        
        Args:
            issue_id: The past health issue ID
            
        Returns:
            The past health issue
            
        Raises:
            HTTPException: If past health issue not found
        """
        issue = self.repository.get_past_health_issue_by_id(issue_id)
        if not issue:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Past health issue not found"
            )
        return issue

    def update_past_health_issue(
        self, past_health_issue: TalentPastHealthIssues
    ) -> TalentPastHealthIssues:
        """Update an existing past health issue.
        
        Args:
            past_health_issue: The updated past health issue data
            
        Returns:
            The updated past health issue
        """
        return self.repository.update_past_health_issue(past_health_issue)

    def delete_past_health_issue(self, issue_id: int) -> bool:
        """Delete a past health issue.
        
        Args:
            issue_id: The past health issue ID to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            HTTPException: If past health issue not found
        """
        deleted = self.repository.delete_past_health_issue(issue_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Past health issue not found"
            )
        return deleted
