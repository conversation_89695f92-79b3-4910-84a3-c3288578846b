import os
import uuid
from typing import Annotated, Any, List, Optional
from fastapi import Depends, HTTPException, status, UploadFile

from app.core.jwt import get_current_user
from app.db import TalentDocumentsCollected, User
from app.repositories.talent import TalentDocumentsRepository


class TalentDocumentsService:
    def __init__(
        self,
        documents_repository: Annotated[TalentDocumentsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.documents_repository = documents_repository
        self.current_user = current_user

    def get_talent_documents(self, talent_id: int) -> List[TalentDocumentsCollected]:
        """Get all documents for a specific talent."""
        return self.documents_repository.get_documents_by_talent_id(talent_id)

    def get_document_by_id(self, document_id: int) -> Optional[TalentDocumentsCollected]:
        """Get a specific document by ID."""
        document = self.documents_repository.get_document_by_id(document_id)
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        return document

    def _create_upload_directory(self, talent_id: int, doc_type: str) -> str:
        """Create upload directory structure if it doesn't exist."""
        upload_dir = f"uploads/talent_docs/{talent_id}/{doc_type}"
        os.makedirs(upload_dir, exist_ok=True)
        return upload_dir

    def _save_uploaded_file(self, file: UploadFile, talent_id: int, doc_type: str) -> str:
        """Save uploaded file and return the file path."""
        # Create directory structure
        upload_dir = self._create_upload_directory(talent_id, doc_type)
        
        # Get file extension
        file_extension = ""
        if file.filename and "." in file.filename:
            file_extension = "." + file.filename.split(".")[-1]
        
        # Generate unique filename to avoid overwriting
        unique_filename = f"{uuid.uuid4()}{file_extension}"
        file_path = os.path.join(upload_dir, unique_filename)
        
        # Save file
        with open(file_path, "wb") as buffer:
            content = file.file.read()
            buffer.write(content)
        
        return file_path

    def create_talent_document(
        self, 
        talent_id: int, 
        doc_type: str, 
        file: UploadFile
    ) -> TalentDocumentsCollected:
        """Create a new talent document with file upload."""
        # Save the uploaded file
        file_path = self._save_uploaded_file(file, talent_id, doc_type)
        
        document = TalentDocumentsCollected(
            talent_profile_id=talent_id,
            doc_type=doc_type,
            url=file_path
        )
        return self.documents_repository.create_document(document)

    def update_document(
        self, 
        document_id: int, 
        doc_type: Optional[str] = None, 
        file: Optional[UploadFile] = None
    ) -> TalentDocumentsCollected:
        """Update an existing document."""
        # First get the existing document to get talent_id
        existing_document = self.documents_repository.get_document_by_id(document_id)
        if not existing_document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        
        update_data: dict[str, Any] = {}
        if doc_type is not None:
            update_data["doc_type"] = doc_type
        
        if file is not None:
            # Use the new doc_type if provided, otherwise use existing doc_type
            file_doc_type = doc_type if doc_type is not None else existing_document.doc_type
            file_path = self._save_uploaded_file(file, existing_document.talent_profile_id, file_doc_type)
            update_data["url"] = file_path

        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No update data provided"
            )

        document = self.documents_repository.update_document(document_id, update_data)
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        return document

    def delete_document(self, document_id: int) -> bool:
        """Delete a document."""
        success = self.documents_repository.delete_document(document_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Document not found"
            )
        return success

    def get_documents_by_type(
        self, 
        talent_id: int, 
        doc_type: str
    ) -> List[TalentDocumentsCollected]:
        """Get documents by talent ID and document type."""
        return self.documents_repository.get_documents_by_type(talent_id, doc_type)
