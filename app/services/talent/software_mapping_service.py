"""Talent Software Mapping service for business logic."""

from typing import Annotated, List
from fastapi import Depends
from app.db.models import User
from app.repositories.talent.software_mapping_repository import TalentSoftwareMappingRepository
from app.schemas.talent.software_mapping_schema import (
    TalentSoftwareMappingCreate,
    TalentSoftwareMappingUpdate,
    TalentSoftwareMappingResponse
)
from app.core.jwt import get_current_user
from app.core.logs import log_error_with_context


class TalentSoftwareMappingService:
    """Service for talent software mapping business logic."""

    def __init__(
        self,
        repository: Annotated[TalentSoftwareMappingRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.repository = repository
        self.current_user = current_user
    
    def create_software_mapping(self, mapping_data: TalentSoftwareMappingCreate) -> TalentSoftwareMappingResponse:
        """Create a new software mapping."""
        try:
            # Check if mapping with same talent and software already exists
            existing_mapping = self.repository.get_by_talent_and_software(
                mapping_data.talent_profile_id, 
                mapping_data.software
            )
            if existing_mapping:
                error = ValueError(
                    f"Software mapping for talent profile {mapping_data.talent_profile_id} "
                    f"and software '{mapping_data.software}' already exists"
                )
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "create_software_mapping",
                        "talent_profile_id": mapping_data.talent_profile_id,
                        "software": mapping_data.software
                    }
                )
                raise error
            
            # Create mapping
            mapping_dict = mapping_data.model_dump()
            mapping = self.repository.create(mapping_dict)
            return TalentSoftwareMappingResponse.model_validate(mapping)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_software_mapping",
                    "talent_profile_id": mapping_data.talent_profile_id,
                    "software": mapping_data.software
                }
            )
            raise e
    
    def get_software_mapping_by_id(self, mapping_id: int) -> TalentSoftwareMappingResponse:
        """Get software mapping by ID."""
        try:
            mapping = self.repository.get_by_id(mapping_id)
            if not mapping:
                error = ValueError(f"Software mapping with ID {mapping_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "get_software_mapping_by_id",
                        "mapping_id": mapping_id
                    }
                )
                raise error
            return TalentSoftwareMappingResponse.model_validate(mapping)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mapping_by_id",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def get_software_mappings_by_talent(self, talent_profile_id: int) -> List[TalentSoftwareMappingResponse]:
        """Get all software mappings for a specific talent profile."""
        try:
            mappings = self.repository.get_by_talent_profile_id(talent_profile_id)
            return [TalentSoftwareMappingResponse.model_validate(mapping) for mapping in mappings]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mappings_by_talent",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def get_software_mappings_by_software(self, software: str) -> List[TalentSoftwareMappingResponse]:
        """Get all mappings for a specific software."""
        try:
            mappings = self.repository.get_by_software(software)
            return [TalentSoftwareMappingResponse.model_validate(mapping) for mapping in mappings]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mappings_by_software",
                    "software": software
                }
            )
            raise e
    
    def get_all_software_mappings(self, skip: int = 0, limit: int = 100) -> List[TalentSoftwareMappingResponse]:
        """Get all software mappings with pagination."""
        try:
            mappings = self.repository.get_all(skip=skip, limit=limit)
            return [TalentSoftwareMappingResponse.model_validate(mapping) for mapping in mappings]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_software_mappings",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def search_software_mappings_by_software(self, software_pattern: str, skip: int = 0, limit: int = 100) -> List[TalentSoftwareMappingResponse]:
        """Search software mappings by software name pattern."""
        try:
            mappings = self.repository.search_by_software(software_pattern, skip=skip, limit=limit)
            return [TalentSoftwareMappingResponse.model_validate(mapping) for mapping in mappings]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "search_software_mappings_by_software",
                    "software_pattern": software_pattern,
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def get_software_mappings_by_version(self, software: str, version: str) -> List[TalentSoftwareMappingResponse]:
        """Get all mappings for a specific software and version."""
        try:
            mappings = self.repository.get_by_software_version(software, version)
            return [TalentSoftwareMappingResponse.model_validate(mapping) for mapping in mappings]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mappings_by_version",
                    "software": software,
                    "version": version
                }
            )
            raise e
    
    def update_software_mapping(self, mapping_id: int, mapping_data: TalentSoftwareMappingUpdate) -> TalentSoftwareMappingResponse:
        """Update software mapping information."""
        try:
            # Get existing mapping
            mapping = self.repository.get_by_id(mapping_id)
            if not mapping:
                error = ValueError(f"Software mapping with ID {mapping_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "update_software_mapping",
                        "mapping_id": mapping_id
                    }
                )
                raise error
            
            # Check if new software conflicts with existing mapping for same talent
            if mapping_data.software and mapping_data.software != mapping.software:
                existing_mapping = self.repository.get_by_talent_and_software(
                    mapping.talent_profile_id, 
                    mapping_data.software
                )
                if existing_mapping and existing_mapping.id != mapping_id:
                    error = ValueError(
                        f"Software mapping for talent profile {mapping.talent_profile_id} "
                        f"and software '{mapping_data.software}' already exists"
                    )
                    log_error_with_context(
                        error=error,
                        context={
                            "operation": "update_software_mapping",
                            "mapping_id": mapping_id,
                            "talent_profile_id": mapping.talent_profile_id,
                            "new_software": mapping_data.software
                        }
                    )
                    raise error
            
            # Update mapping
            update_dict = mapping_data.model_dump()
            updated_mapping = self.repository.update(mapping, update_dict)
            return TalentSoftwareMappingResponse.model_validate(updated_mapping)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_software_mapping",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def delete_software_mapping(self, mapping_id: int) -> None:
        """Delete a software mapping."""
        try:
            mapping = self.repository.get_by_id(mapping_id)
            if not mapping:
                error = ValueError(f"Software mapping with ID {mapping_id} not found")
                log_error_with_context(
                    error=error,
                    context={
                        "operation": "delete_software_mapping",
                        "mapping_id": mapping_id
                    }
                )
                raise error
            
            self.repository.delete(mapping)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_software_mapping",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def get_software_mapping_count(self) -> int:
        """Get total count of software mappings."""
        try:
            return self.repository.count_total()
        except Exception as e:
            log_error_with_context(
                error=e,
                context={"operation": "get_software_mapping_count"}
            )
            raise e
    
    def get_software_mapping_count_by_talent(self, talent_profile_id: int) -> int:
        """Get count of software mappings for a specific talent profile."""
        try:
            return self.repository.count_by_talent_profile(talent_profile_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_software_mapping_count_by_talent",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
