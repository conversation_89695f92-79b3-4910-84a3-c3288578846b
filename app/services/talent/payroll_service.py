"""Payroll information service for talent management.

This module contains the service class for payroll information business logic.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db.models import User
from app.repositories.talent import PayrollRepository
from app.schemas.talent.payroll_schema import (
    TalentPayrollInformationCreate,
    TalentPayrollInformationUpdate,
    TalentPayrollInformationResponse
)
from app.core.logs import log_error_with_context


class PayrollService:
    """Service class for payroll information business logic.
    
    Handles all business operations related to payroll information.
    """
    
    def __init__(
        self, 
        repository: Annotated[PayrollRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            session: SQLModel database session
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_payroll_information(
        self, 
        payroll_data: TalentPayrollInformationCreate
    ) -> Optional[TalentPayrollInformationResponse]:
        """Create new payroll information for a talent.
        
        Args:
            payroll_data: Payroll information data to create
            
        Returns:
            Created payroll information response or None if creation fails
        """
        try:
            # Check if payroll information already exists for this talent
            if self.repository.exists_by_talent_id(payroll_data.talent_profile_id):
                log_error_with_context(
                    error=ValueError("Payroll information already exists for this talent"),
                    context={
                        "talent_profile_id": payroll_data.talent_profile_id,
                        "operation": "create_payroll_information"
                    }
                )
                return None
            
            db_payroll = self.repository.create(payroll_data)
            if db_payroll:
                return TalentPayrollInformationResponse.model_validate(db_payroll)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_payroll_information",
                    "talent_profile_id": payroll_data.talent_profile_id
                }
            )
            raise e
    
    def get_payroll_information_by_id(self, payroll_id: int) -> Optional[TalentPayrollInformationResponse]:
        """Get payroll information by ID.
        
        Args:
            payroll_id: Payroll information ID
            
        Returns:
            Payroll information response or None if not found
        """
        try:
            db_payroll = self.repository.get_by_id(payroll_id)
            if db_payroll:
                return TalentPayrollInformationResponse.model_validate(db_payroll)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_payroll_information_by_id",
                    "payroll_id": payroll_id
                }
            )
            raise e
    
    def get_payroll_information_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> Optional[TalentPayrollInformationResponse]:
        """Get payroll information for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            Payroll information response or None if not found
        """
        try:
            db_payroll = self.repository.get_by_talent_id(talent_profile_id)
            if db_payroll:
                return TalentPayrollInformationResponse.model_validate(db_payroll)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_payroll_information_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def update_payroll_information(
        self, 
        payroll_id: int, 
        payroll_data: TalentPayrollInformationUpdate
    ) -> Optional[TalentPayrollInformationResponse]:
        """Update payroll information.
        
        Args:
            payroll_id: Payroll information ID
            payroll_data: Updated payroll information data
            
        Returns:
            Updated payroll information response or None if update fails
        """
        try:
            # Check if payroll information exists
            existing_payroll = self.repository.get_by_id(payroll_id)

            if not existing_payroll:
                log_error_with_context(
                    error=ValueError("Payroll information not found"),
                    context={
                        "payroll_id": payroll_id,
                        "operation": "update_payroll_information"
                    }
                )
                raise ValueError("Payroll information not found")

            # Update fields
            update_data = payroll_data.model_dump()
            for field, value in update_data.items():
                if value is not None:
                    setattr(existing_payroll, field, value)
            
            db_payroll = self.repository.update(payroll_id, existing_payroll)
            if db_payroll:
                return TalentPayrollInformationResponse.model_validate(db_payroll)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_payroll_information",
                    "payroll_id": payroll_id
                }
            )
            raise e
    
    def delete_payroll_information(self, payroll_id: int) -> bool:
        """Delete payroll information.
        
        Args:
            payroll_id: Payroll information ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Check if payroll information exists
            existing_payroll = self.repository.get_by_id(payroll_id)
            if not existing_payroll:
                log_error_with_context(
                    error=ValueError("Payroll information not found"),
                    context={
                        "payroll_id": payroll_id,
                        "operation": "delete_payroll_information"
                    }
                )
                return False
            
            return self.repository.delete(payroll_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_payroll_information",
                    "payroll_id": payroll_id
                }
            )
            raise e
    
    def validate_payroll_information(self, payroll_data: TalentPayrollInformationCreate) -> List[str]:
        """Validate payroll information data.
        
        Args:
            payroll_data: Payroll information data to validate
            
        Returns:
            List of validation error messages
        """
        errors: List[str] = []
        
        try:
            # Validate IMMS format (basic validation - should be numeric)
            if payroll_data.imms and not payroll_data.imms.isdigit():
                errors.append("IMMS number must contain only digits")
            
            # Validate CURP format (18 characters for Mexican CURP)
            if payroll_data.curp:
                if len(payroll_data.curp) != 18:
                    errors.append("CURP must be exactly 18 characters")
                
                if not payroll_data.curp.isalnum():
                    errors.append("CURP must contain only alphanumeric characters")
            
            # Validate RFC format (12 or 13 characters for Mexican RFC)
            if payroll_data.rfc:
                rfc_len = len(payroll_data.rfc)
                if rfc_len not in [12, 13]:
                    errors.append("RFC must be 12 or 13 characters long")
                
                if not payroll_data.rfc.isalnum():
                    errors.append("RFC must contain only alphanumeric characters")
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "validate_payroll_information",
                    "talent_profile_id": payroll_data.talent_profile_id
                }
            )
            errors.append("Validation error occurred")
        
        return errors
