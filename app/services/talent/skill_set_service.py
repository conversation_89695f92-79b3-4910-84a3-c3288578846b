"""Skill set service for talent management.

This module contains the service class for talent skill set business logic operations.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.skill_set_repository import TalentSkillSetRepository
from app.schemas.talent.skill_set_schema import (
    TalentSkillSetCreate,
    TalentSkillSetUpdate,
    TalentSkillSetResponse
)
from app.core.logs import log_error_with_context


class TalentSkillSetService:
    """Service class for talent skill set business logic.
    
    Handles all business operations related to talent skill sets.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentSkillSetRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_skill_set(self, skill_set_data: TalentSkillSetCreate) -> Optional[TalentSkillSetResponse]:
        """Create a new talent skill set.
        
        Args:
            skill_set_data: Skill set data to create
            
        Returns:
            Created skill set response or None if creation fails
        """
        try:
            # Validate business rules if needed
            if not self._validate_skill_set_data(skill_set_data):
                return None
            
            db_skill_set = self.repository.create(skill_set_data)
            if db_skill_set:
                return TalentSkillSetResponse.model_validate(db_skill_set)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_skill_set",
                    "service": "TalentSkillSetService",
                    "data": skill_set_data.model_dump()
                }
            )
            return None
    
    def get_skill_set_by_id(self, skill_set_id: int) -> Optional[TalentSkillSetResponse]:
        """Get a talent skill set by ID.
        
        Args:
            skill_set_id: Skill set ID
            
        Returns:
            Skill set response or None if not found
        """
        try:
            db_skill_set = self.repository.get_by_id(skill_set_id)
            if db_skill_set:
                return TalentSkillSetResponse.model_validate(db_skill_set)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_skill_set_by_id",
                    "service": "TalentSkillSetService",
                    "data": {"skill_set_id": skill_set_id}
                }
            )
            return None
    
    def get_skill_sets_by_talent_id(self, talent_profile_id: int) -> List[TalentSkillSetResponse]:
        """Get all skill sets for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of skill set responses for the talent
        """
        try:
            db_skill_sets = self.repository.get_by_talent_id(talent_profile_id)
            return [TalentSkillSetResponse.model_validate(skill_set) for skill_set in db_skill_sets]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_skill_sets_by_talent_id",
                    "service": "TalentSkillSetService",
                    "data": {"talent_profile_id": talent_profile_id}
                }
            )
            return []
    
    def get_all_skill_sets(self, skip: int = 0, limit: int = 100) -> List[TalentSkillSetResponse]:
        """Get all talent skill sets with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of skill set responses
        """
        try:
            db_skill_sets = self.repository.get_all(skip=skip, limit=limit)
            return [TalentSkillSetResponse.model_validate(skill_set) for skill_set in db_skill_sets]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_skill_sets",
                    "service": "TalentSkillSetService",
                    "data": {"skip": skip, "limit": limit}
                }
            )
            return []
    
    def update_skill_set(self, skill_set_id: int, skill_set_data: TalentSkillSetUpdate) -> Optional[TalentSkillSetResponse]:
        """Update an existing talent skill set.
        
        Args:
            skill_set_id: Skill set ID to update
            skill_set_data: Updated skill set data
            
        Returns:
            Updated skill set response or None if update fails
        """
        try:
            # Check if skill set exists
            existing_skill_set = self.repository.get_by_id(skill_set_id)
            if not existing_skill_set:
                return None
            
            # Validate update data if needed
            if not self._validate_skill_set_update(skill_set_data):
                return None
            
            # Convert update data to full model for repository
            update_dict = skill_set_data.model_dump()
            for field, value in update_dict.items():
                if hasattr(existing_skill_set, field):
                    setattr(existing_skill_set, field, value)
            
            db_skill_set = self.repository.update(skill_set_id, existing_skill_set)
            if db_skill_set:
                return TalentSkillSetResponse.model_validate(db_skill_set)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_skill_set",
                    "service": "TalentSkillSetService",
                    "data": {"skill_set_id": skill_set_id}
                }
            )
            return None
    
    def delete_skill_set(self, skill_set_id: int) -> bool:
        """Delete a talent skill set.
        
        Args:
            skill_set_id: Skill set ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            return self.repository.delete(skill_set_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_skill_set",
                    "service": "TalentSkillSetService",
                    "data": {"skill_set_id": skill_set_id}
                }
            )
            return False
    
    def skill_set_exists_for_talent(self, talent_profile_id: int) -> bool:
        """Check if skill set records exist for a talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            True if skill set records exist, False otherwise
        """
        try:
            return self.repository.exists_by_talent_id(talent_profile_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "skill_set_exists_for_talent",
                    "service": "TalentSkillSetService",
                    "data": {"talent_profile_id": talent_profile_id}
                }
            )
            return False
    
    def _validate_skill_set_data(self, skill_set_data: TalentSkillSetCreate) -> bool:
        """Validate skill set creation data.
        
        Args:
            skill_set_data: Skill set data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        # Add business validation rules here
        if not skill_set_data.skills or len(skill_set_data.skills) == 0:
            return False
        
        if skill_set_data.years_of_experience < 0:
            return False
        
        if not skill_set_data.english_level or len(skill_set_data.english_level) == 0:
            return False
        
        # Validate english_level contains valid values
        valid_english_levels = ["Write", "Speak", "Type"]
        for level in skill_set_data.english_level:
            if level not in valid_english_levels:
                return False
        
        return True
    
    def _validate_skill_set_update(self, skill_set_data: TalentSkillSetUpdate) -> bool:
        """Validate skill set update data.
        
        Args:
            skill_set_data: Skill set update data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        # Add business validation rules for updates
        if skill_set_data.skills is not None and len(skill_set_data.skills) == 0:
            return False
        
        if skill_set_data.years_of_experience is not None and skill_set_data.years_of_experience < 0:
            return False
        
        if skill_set_data.english_level is not None:
            if len(skill_set_data.english_level) == 0:
                return False
            
            # Validate english_level contains valid values
            valid_english_levels = ["Write", "Speak", "Type"]
            for level in skill_set_data.english_level:
                if level not in valid_english_levels:
                    return False
        
        return True
