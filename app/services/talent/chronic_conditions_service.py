"""Service layer for managing talent chronic conditions."""

from typing import Annotated, List

from fastapi import Depends, HTTPException, status

from app.core.jwt import get_current_user
from app.db import TalentCronicConditions, User
from app.repositories.talent import TalentChronicConditionsRepository


class TalentChronicConditionsService:
    """Service class for talent chronic conditions business logic."""
    
    def __init__(
        self,
        chronic_conditions_repository: Annotated[TalentChronicConditionsRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)],
    ) -> None:
        """Initialize the service with dependencies.
        
        Args:
            chronic_conditions_repository: Repository for chronic conditions
            current_user: Currently authenticated user
        """
        self.chronic_conditions_repository = chronic_conditions_repository
        self.current_user = current_user

    def create_chronic_condition(
        self, chronic_condition: TalentCronicConditions
    ) -> TalentCronicConditions:
        """Create a new chronic condition.
        
        Args:
            chronic_condition: The chronic condition data to create
            
        Returns:
            The created chronic condition
        """
        return self.chronic_conditions_repository.create_chronic_condition(chronic_condition)

    def get_chronic_conditions_by_talent_id(
        self, talent_id: int
    ) -> List[TalentCronicConditions]:
        """Get all chronic conditions for a talent.
        
        Args:
            talent_id: The talent profile ID
            
        Returns:
            List of chronic conditions
        """
        return self.chronic_conditions_repository.get_chronic_conditions_by_talent_id(talent_id)

    def get_chronic_condition_by_id(
        self, condition_id: int
    ) -> TalentCronicConditions:
        """Get a chronic condition by ID.
        
        Args:
            condition_id: The chronic condition ID
            
        Returns:
            The chronic condition
            
        Raises:
            HTTPException: If chronic condition not found
        """
        condition = self.chronic_conditions_repository.get_chronic_condition_by_id(condition_id)
        if not condition:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chronic condition not found"
            )
        return condition

    def update_chronic_condition(
        self, chronic_condition: TalentCronicConditions
    ) -> TalentCronicConditions:
        """Update an existing chronic condition.
        
        Args:
            chronic_condition: The updated chronic condition data
            
        Returns:
            The updated chronic condition
        """
        return self.chronic_conditions_repository.update_chronic_condition(chronic_condition)

    def delete_chronic_condition(self, condition_id: int) -> bool:
        """Delete a chronic condition.
        
        Args:
            condition_id: The chronic condition ID to delete
            
        Returns:
            True if deleted successfully
            
        Raises:
            HTTPException: If chronic condition not found
        """
        deleted = self.chronic_conditions_repository.delete_chronic_condition(condition_id)
        if not deleted:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Chronic condition not found"
            )
        return deleted
