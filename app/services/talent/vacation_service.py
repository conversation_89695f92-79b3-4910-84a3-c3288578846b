"""Vacation mapping service for talent management.

This module contains the service class for vacation mapping business logic.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.vacation_repository import VacationRepository
from app.schemas.talent.vacation_schema import (
    TalentVacationMappingCreate,
    TalentVacationMappingUpdate,
    TalentVacationMappingResponse
)
from app.core.logs import log_error_with_context


class VacationService:
    """Service class for vacation mapping business logic.
    
    Handles all business operations related to vacation mapping.
    """
    
    def __init__(
        self, 
        repository: Annotated[VacationRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a repository and current user.
        
        Args:
            repository: Injected vacation repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_vacation_mapping(
        self, 
        vacation_data: TalentVacationMappingCreate
    ) -> Optional[TalentVacationMappingResponse]:
        """Create new vacation mapping for a talent.
        
        Args:
            vacation_data: Vacation mapping data to create
            
        Returns:
            Created vacation mapping response or None if creation fails
        """
        try:
            # Check if vacation mapping already exists for this talent and year
            if self.repository.exists_by_talent_and_year(
                vacation_data.talent_profile_id, 
                vacation_data.year_number
            ):
                log_error_with_context(
                    error=ValueError("Vacation mapping already exists for this talent and year"),
                    context={
                        "talent_profile_id": vacation_data.talent_profile_id,
                        "year_number": vacation_data.year_number,
                        "operation": "create_vacation_mapping"
                    }
                )
                return None
            
            db_vacation = self.repository.create(vacation_data)
            if db_vacation:
                return TalentVacationMappingResponse.model_validate(db_vacation)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_vacation_mapping",
                    "talent_profile_id": vacation_data.talent_profile_id
                }
            )
            raise e
    
    def get_vacation_mapping_by_id(self, vacation_id: int) -> Optional[TalentVacationMappingResponse]:
        """Get vacation mapping by ID.
        
        Args:
            vacation_id: Vacation mapping ID
            
        Returns:
            Vacation mapping response or None if not found
        """
        try:
            db_vacation = self.repository.get_by_id(vacation_id)
            if db_vacation:
                return TalentVacationMappingResponse.model_validate(db_vacation)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_vacation_mapping_by_id",
                    "vacation_id": vacation_id
                }
            )
            raise e
    
    def get_vacation_mapping_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> List[TalentVacationMappingResponse]:
        """Get all vacation mappings for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of vacation mapping responses
        """
        try:
            db_vacation_list = self.repository.get_by_talent_id(talent_profile_id)
            return [
                TalentVacationMappingResponse.model_validate(db_vacation)
                for db_vacation in db_vacation_list
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_vacation_mapping_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def get_vacation_mapping_by_talent_and_year(
        self, 
        talent_profile_id: int, 
        year_number: int
    ) -> Optional[TalentVacationMappingResponse]:
        """Get vacation mapping for a specific talent and year.
        
        Args:
            talent_profile_id: Talent profile ID
            year_number: Year number
            
        Returns:
            Vacation mapping response or None if not found
        """
        try:
            db_vacation = self.repository.get_by_talent_and_year(talent_profile_id, year_number)
            if db_vacation:
                return TalentVacationMappingResponse.model_validate(db_vacation)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_vacation_mapping_by_talent_and_year",
                    "talent_profile_id": talent_profile_id,
                    "year_number": year_number
                }
            )
            raise e
    
    def update_vacation_mapping(
        self, 
        vacation_id: int, 
        vacation_data: TalentVacationMappingUpdate
    ) -> Optional[TalentVacationMappingResponse]:
        """Update vacation mapping.
        
        Args:
            vacation_id: Vacation mapping ID
            vacation_data: Updated vacation mapping data
            
        Returns:
            Updated vacation mapping response or None if update fails
        """
        try:
            # Check if vacation mapping exists
            existing_vacation = self.repository.get_by_id(vacation_id)

            if not existing_vacation:
                log_error_with_context(
                    error=ValueError("Vacation mapping not found"),
                    context={
                        "vacation_id": vacation_id,
                        "operation": "update_vacation_mapping"
                    }
                )
                raise ValueError("Vacation mapping not found")
            
            # If year number is being updated, check for duplicates
            if vacation_data.year_number and vacation_data.year_number != existing_vacation.year_number:
                if self.repository.exists_by_talent_and_year(
                    existing_vacation.talent_profile_id, 
                    vacation_data.year_number
                ):
                    log_error_with_context(
                        error=ValueError("Vacation mapping already exists for this talent and year"),
                        context={
                            "talent_profile_id": existing_vacation.talent_profile_id,
                            "year_number": vacation_data.year_number,
                            "operation": "update_vacation_mapping"
                        }
                    )
                    return None

            # Update fields
            update_data = vacation_data.model_dump()
            for field, value in update_data.items():
                if value is not None:
                    setattr(existing_vacation, field, value)
            
            db_vacation = self.repository.update(vacation_id, existing_vacation)
            if db_vacation:
                return TalentVacationMappingResponse.model_validate(db_vacation)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_vacation_mapping",
                    "vacation_id": vacation_id
                }
            )
            raise e
    
    def delete_vacation_mapping(self, vacation_id: int) -> bool:
        """Delete vacation mapping.
        
        Args:
            vacation_id: Vacation mapping ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            return self.repository.delete(vacation_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_vacation_mapping",
                    "vacation_id": vacation_id
                }
            )
            raise e