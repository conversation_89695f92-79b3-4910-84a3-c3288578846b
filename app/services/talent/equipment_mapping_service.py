"""Talent equipment mapping service for talent management.

This module contains the service class for talent equipment mapping business logic.
"""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.equipment_mapping_repository import TalentEquipmentMappingRepository
from app.schemas.talent.equipment_mapping_schema import (
    TalentEquipmentMappingCreate,
    TalentEquipmentMappingUpdate,
    TalentEquipmentMappingResponse
)
from app.core.logs import log_error_with_context


class TalentEquipmentMappingService:
    """Service class for talent equipment mapping business logic.
    
    Handles all business operations related to talent equipment mapping.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentEquipmentMappingRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_equipment_mapping(
        self, 
        mapping_data: TalentEquipmentMappingCreate
    ) -> Optional[TalentEquipmentMappingResponse]:
        """Create new equipment mapping for a talent.
        
        Args:
            mapping_data: Equipment mapping data to create
            
        Returns:
            Created equipment mapping response or None if creation fails
        """
        try:
            # Check if equipment mapping already exists for this talent and equipment
            if self.repository.exists_by_talent_and_equipment(
                mapping_data.talent_profile_id, 
                mapping_data.equipment_id
            ):
                log_error_with_context(
                    error=ValueError("Equipment mapping already exists for this talent and equipment"),
                    context={
                        "talent_profile_id": mapping_data.talent_profile_id,
                        "equipment_id": mapping_data.equipment_id,
                        "operation": "create_equipment_mapping"
                    }
                )
                return None
            
            db_mapping = self.repository.create(mapping_data)
            if db_mapping:
                return TalentEquipmentMappingResponse.model_validate(db_mapping)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_equipment_mapping",
                    "talent_profile_id": mapping_data.talent_profile_id,
                    "equipment_id": mapping_data.equipment_id
                }
            )
            raise e
    
    def get_equipment_mapping_by_id(self, mapping_id: int) -> Optional[TalentEquipmentMappingResponse]:
        """Get equipment mapping by ID.
        
        Args:
            mapping_id: Equipment mapping ID
            
        Returns:
            Equipment mapping response or None if not found
        """
        try:
            db_mapping = self.repository.get_by_id(mapping_id)
            if db_mapping:
                return TalentEquipmentMappingResponse.model_validate(db_mapping)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_mapping_by_id",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def get_equipment_mappings_by_talent_id(
        self, 
        talent_profile_id: int
    ) -> List[TalentEquipmentMappingResponse]:
        """Get equipment mappings for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of equipment mapping responses
        """
        try:
            db_mappings = self.repository.get_by_talent_id(talent_profile_id)
            return [
                TalentEquipmentMappingResponse.model_validate(db_mapping)
                for db_mapping in db_mappings
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_mappings_by_talent_id",
                    "talent_profile_id": talent_profile_id
                }
            )
            raise e
    
    def get_equipment_mappings_by_equipment_id(
        self, 
        equipment_id: int
    ) -> List[TalentEquipmentMappingResponse]:
        """Get all talent equipment mappings for a specific equipment.
        
        Args:
            equipment_id: Equipment ID
            
        Returns:
            List of equipment mapping responses
        """
        try:
            db_mappings = self.repository.get_by_equipment_id(equipment_id)
            return [
                TalentEquipmentMappingResponse.model_validate(db_mapping)
                for db_mapping in db_mappings
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_mappings_by_equipment_id",
                    "equipment_id": equipment_id
                }
            )
            raise e
    
    def get_active_equipment_mappings(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[TalentEquipmentMappingResponse]:
        """Get all active (not replaced) talent equipment mappings with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of active equipment mapping responses
        """
        try:
            db_mappings = self.repository.get_active_mappings(skip=skip, limit=limit)
            return [
                TalentEquipmentMappingResponse.model_validate(db_mapping)
                for db_mapping in db_mappings
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_active_equipment_mappings",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def get_all_equipment_mappings(
        self, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[TalentEquipmentMappingResponse]:
        """Get all talent equipment mappings with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of equipment mapping responses
        """
        try:
            db_mappings = self.repository.get_all(skip=skip, limit=limit)
            return [
                TalentEquipmentMappingResponse.model_validate(db_mapping)
                for db_mapping in db_mappings
            ]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_equipment_mappings",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise e
    
    def update_equipment_mapping(
        self, 
        mapping_id: int, 
        mapping_data: TalentEquipmentMappingUpdate
    ) -> Optional[TalentEquipmentMappingResponse]:
        """Update equipment mapping.
        
        Args:
            mapping_id: Equipment mapping ID
            mapping_data: Updated equipment mapping data
            
        Returns:
            Updated equipment mapping response or None if update fails
        """
        try:
            # Check if equipment mapping exists
            existing_mapping = self.repository.get_by_id(mapping_id)

            if not existing_mapping:
                log_error_with_context(
                    error=ValueError("Equipment mapping not found"),
                    context={
                        "mapping_id": mapping_id,
                        "operation": "update_equipment_mapping"
                    }
                )
                raise ValueError("Equipment mapping not found")

            # Update fields
            update_data = mapping_data.model_dump()
            for field, value in update_data.items():
                if hasattr(existing_mapping, field):
                    setattr(existing_mapping, field, value)
            
            db_mapping = self.repository.update(mapping_id, existing_mapping)
            if db_mapping:
                return TalentEquipmentMappingResponse.model_validate(db_mapping)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_equipment_mapping",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def delete_equipment_mapping(self, mapping_id: int) -> bool:
        """Delete equipment mapping.
        
        Args:
            mapping_id: Equipment mapping ID
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            # Check if equipment mapping exists
            existing_mapping = self.repository.get_by_id(mapping_id)
            if not existing_mapping:
                log_error_with_context(
                    error=ValueError("Equipment mapping not found"),
                    context={
                        "mapping_id": mapping_id,
                        "operation": "delete_equipment_mapping"
                    }
                )
                return False
            
            return self.repository.delete(mapping_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_equipment_mapping",
                    "mapping_id": mapping_id
                }
            )
            raise e
    
    def validate_equipment_mapping(self, mapping_data: TalentEquipmentMappingCreate) -> List[str]:
        """Validate equipment mapping data.
        
        Args:
            mapping_data: Equipment mapping data to validate
            
        Returns:
            List of validation error messages
        """
        errors: List[str] = []
        
        try:
            # Validate required fields
            if not mapping_data.talent_profile_id:
                errors.append("Talent profile ID is required")
            
            if not mapping_data.equipment_id:
                errors.append("Equipment ID is required")
            
            # Validate duration of use if provided
            if mapping_data.duration_of_use and len(mapping_data.duration_of_use.strip()) == 0:
                errors.append("Duration of use cannot be empty if provided")
            
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "validate_equipment_mapping",
                    "talent_profile_id": mapping_data.talent_profile_id,
                    "equipment_id": mapping_data.equipment_id
                }
            )
            errors.append("Validation error occurred")
        
        return errors
