"""Position service for talent management.

This module contains the service class for talent position business logic operations."""

from typing import Annotated, List, Optional
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories.talent.position_repository import TalentPositionRepository
from app.schemas.talent.position_schema import (
    TalentPositionCreate,
    TalentPositionUpdate,
    TalentPositionResponse
)
from app.core.logs import log_error_with_context


class TalentPositionService:
    """Service class for talent position business logic.
    
    Handles all business operations related to talent positions.
    """
    
    def __init__(
        self, 
        repository: Annotated[TalentPositionRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with a database session and current user.
        
        Args:
            repository: Injected repository
            current_user: Current authenticated user
        """
        self.repository = repository
        self.current_user = current_user
    
    def create_position(self, position_data: TalentPositionCreate) -> Optional[TalentPositionResponse]:
        """Create a new talent position.
        
        Args:
            position_data: Position data to create
            
        Returns:
            Created position response or None if creation fails
        """
        try:
            # Validate business rules if needed
            if not self._validate_position_data(position_data):
                return None
            
            db_position = self.repository.create(position_data)
            if db_position:
                return TalentPositionResponse.model_validate(db_position)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "create_position",
                    "service": "TalentPositionService",
                    "data": position_data.model_dump()
                }
            )
            return None
    
    def get_position_by_id(self, position_id: int) -> Optional[TalentPositionResponse]:
        """Get a talent position by ID.
        
        Args:
            position_id: Position ID
            
        Returns:
            Position response or None if not found
        """
        try:
            db_position = self.repository.get_by_id(position_id)
            if db_position:
                return TalentPositionResponse.model_validate(db_position)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_position_by_id",
                    "service": "TalentPositionService",
                    "data": {"position_id": position_id}
                }
            )
            return None
    
    def get_positions_by_talent_id(self, talent_profile_id: int) -> List[TalentPositionResponse]:
        """Get all positions for a specific talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            List of position responses for the talent
        """
        try:
            db_positions = self.repository.get_by_talent_id(talent_profile_id)
            return [TalentPositionResponse.model_validate(position) for position in db_positions]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_positions_by_talent_id",
                    "service": "TalentPositionService",
                    "data": {"talent_profile_id": talent_profile_id}
                }
            )
            return []
    
    def get_all_positions(self, skip: int = 0, limit: int = 100) -> List[TalentPositionResponse]:
        """Get all talent positions with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of position responses
        """
        try:
            db_positions = self.repository.get_all(skip=skip, limit=limit)
            return [TalentPositionResponse.model_validate(position) for position in db_positions]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_positions",
                    "service": "TalentPositionService",
                    "data": {"skip": skip, "limit": limit}
                }
            )
            return []
    
    def update_position(self, position_id: int, position_data: TalentPositionUpdate) -> Optional[TalentPositionResponse]:
        """Update an existing talent position.
        
        Args:
            position_id: Position ID to update
            position_data: Updated position data
            
        Returns:
            Updated position response or None if update fails
        """
        try:
            # Check if position exists
            existing_position = self.repository.get_by_id(position_id)
            if not existing_position:
                return None
            
            # Validate update data if needed
            if not self._validate_position_update(position_data):
                return None
            
            # Convert update data to full model for repository
            update_dict = position_data.model_dump()
            for field, value in update_dict.items():
                if hasattr(existing_position, field):
                    setattr(existing_position, field, value)
            
            db_position = self.repository.update(position_id, existing_position)
            if db_position:
                return TalentPositionResponse.model_validate(db_position)
            return None
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_position",
                    "service": "TalentPositionService",
                    "data": {"position_id": position_id}
                }
            )
            return None
    
    def delete_position(self, position_id: int) -> bool:
        """Delete a talent position.
        
        Args:
            position_id: Position ID to delete
            
        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            return self.repository.delete(position_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_position",
                    "service": "TalentPositionService",
                    "data": {"position_id": position_id}
                }
            )
            return False
    
    def position_exists_for_talent(self, talent_profile_id: int) -> bool:
        """Check if position records exist for a talent.
        
        Args:
            talent_profile_id: Talent profile ID
            
        Returns:
            True if position records exist, False otherwise
        """
        try:
            return self.repository.exists_by_talent_id(talent_profile_id)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "position_exists_for_talent",
                    "service": "TalentPositionService",
                    "data": {"talent_profile_id": talent_profile_id}
                }
            )
            return False
    
    def _validate_position_data(self, position_data: TalentPositionCreate) -> bool:
        """Validate position creation data.
        
        Args:
            position_data: Position data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        # Add business validation rules here
        if not position_data.position or len(position_data.position.strip()) == 0:
            return False
        
        if not position_data.title or len(position_data.title.strip()) == 0:
            return False
        
        if not position_data.role or len(position_data.role.strip()) == 0:
            return False
        
        # Validate date ranges if both start and end dates are provided
        if position_data.start_date and position_data.end_date:
            if position_data.start_date > position_data.end_date:
                return False
        
        return True
    
    def _validate_position_update(self, position_data: TalentPositionUpdate) -> bool:
        """Validate position update data.
        
        Args:
            position_data: Position update data to validate
            
        Returns:
            True if data is valid, False otherwise
        """
        # Add business validation rules for updates
        if position_data.position is not None and len(position_data.position.strip()) == 0:
            return False
        
        if position_data.title is not None and len(position_data.title.strip()) == 0:
            return False
        
        if position_data.role is not None and len(position_data.role.strip()) == 0:
            return False
        
        # Validate date ranges if both start and end dates are provided
        if position_data.start_date and position_data.end_date:
            if position_data.start_date > position_data.end_date:
                return False
        
        return True
