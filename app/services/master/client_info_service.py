"""Master Client Info service for business logic."""

from typing import Annotated, List
from fastapi import Depends
from app.db.models import User
from app.repositories.master.client_info_repository import MasterClientInfoRepository
from app.schemas.master.client_info_schema import (
    MasterClientInfoCreate,
    MasterClientInfoUpdate,
    MasterClientInfoResponse
)
from app.core.jwt import get_current_user


class MasterClientInfoService:
    """Service for master client info business logic."""

    def __init__(
        self,
        repository: Annotated[MasterClientInfoRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.repository = repository
        self.current_user = current_user
    
    def create_client(self, client_data: MasterClientInfoCreate) -> MasterClientInfoResponse:
        """Create a new client."""
        # Check if client with same name already exists
        existing_client = self.repository.get_by_name(client_data.name)
        if existing_client:
            raise ValueError(f"Client with name '{client_data.name}' already exists")
        
        # Check if client with same email already exists
        if client_data.email:
            existing_email = self.repository.get_by_email(client_data.email)
            if existing_email:
                raise ValueError(f"Client with email '{client_data.email}' already exists")
        
        # Create client
        client_dict = client_data.model_dump()
        client = self.repository.create(client_dict)
        return MasterClientInfoResponse.model_validate(client)
    
    def get_client_by_id(self, client_id: int) -> MasterClientInfoResponse:
        """Get client by ID."""
        client = self.repository.get_by_id(client_id)
        if not client:
            raise ValueError(f"Client with ID {client_id} not found")
        return MasterClientInfoResponse.model_validate(client)
    
    def get_client_by_name(self, name: str) -> MasterClientInfoResponse:
        """Get client by name."""
        client = self.repository.get_by_name(name)
        if not client:
            raise ValueError(f"Client with name '{name}' not found")
        return MasterClientInfoResponse.model_validate(client)
    
    def get_client_by_email(self, email: str) -> MasterClientInfoResponse:
        """Get client by email."""
        client = self.repository.get_by_email(email)
        if not client:
            raise ValueError(f"Client with email '{email}' not found")
        return MasterClientInfoResponse.model_validate(client)
    
    def get_all_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientInfoResponse]:
        """Get all clients with pagination."""
        clients = self.repository.get_all(skip=skip, limit=limit)
        return [MasterClientInfoResponse.model_validate(client) for client in clients]
    
    def get_active_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientInfoResponse]:
        """Get all active clients."""
        clients = self.repository.get_active_clients(skip=skip, limit=limit)
        return [MasterClientInfoResponse.model_validate(client) for client in clients]
    
    def get_inactive_clients(self, skip: int = 0, limit: int = 100) -> List[MasterClientInfoResponse]:
        """Get all inactive clients."""
        clients = self.repository.get_inactive_clients(skip=skip, limit=limit)
        return [MasterClientInfoResponse.model_validate(client) for client in clients]
    
    def search_clients_by_name(self, name_pattern: str, skip: int = 0, limit: int = 100) -> List[MasterClientInfoResponse]:
        """Search clients by name pattern."""
        clients = self.repository.search_by_name(name_pattern, skip=skip, limit=limit)
        return [MasterClientInfoResponse.model_validate(client) for client in clients]
    
    def update_client(self, client_id: int, client_data: MasterClientInfoUpdate) -> MasterClientInfoResponse:
        """Update client information."""
        # Get existing client
        client = self.repository.get_by_id(client_id)
        if not client:
            raise ValueError(f"Client with ID {client_id} not found")
        
        # Check if new name conflicts with existing client
        if client_data.name and client_data.name != client.name:
            existing_client = self.repository.get_by_name(client_data.name)
            if existing_client:
                raise ValueError(f"Client with name '{client_data.name}' already exists")
        
        # Check if new email conflicts with existing client
        if client_data.email and client_data.email != client.email:
            existing_email = self.repository.get_by_email(client_data.email)
            if existing_email:
                raise ValueError(f"Client with email '{client_data.email}' already exists")
        
        # Update client
        update_dict = client_data.model_dump()
        updated_client = self.repository.update(client, update_dict)
        return MasterClientInfoResponse.model_validate(updated_client)
    
    def delete_client(self, client_id: int) -> None:
        """Delete a client (admin only)."""
        if not self.current_user.is_superuser:
            raise ValueError("Only administrators can delete clients")
        
        client = self.repository.get_by_id(client_id)
        if not client:
            raise ValueError(f"Client with ID {client_id} not found")
        
        self.repository.delete(client)
    
    def toggle_client_status(self, client_id: int) -> MasterClientInfoResponse:
        """Toggle client active status."""
        client = self.repository.get_by_id(client_id)
        if not client:
            raise ValueError(f"Client with ID {client_id} not found")
        
        updated_client = self.repository.toggle_status(client)
        return MasterClientInfoResponse.model_validate(updated_client)
