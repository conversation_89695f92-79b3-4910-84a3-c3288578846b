"""Service for master equipment management.

This module contains the service class for master equipment business logic.
"""

from typing import List, Annotated
from fastapi import Depends, HTTPException, status
from app.repositories.master.equipment_repository import EquipmentRepository
from app.schemas.master.equipment_schema import (
    MasterEquipmentCreate,
    MasterEquipmentUpdate,
    MasterEquipmentResponse
)
from app.core.jwt import get_current_user
from app.db import User
from app.core.logs import log_error_with_context


class EquipmentService:
    """Service class for master equipment business logic."""
    
    def __init__(
        self,
        equipment_repository: Annotated[EquipmentRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        """Initialize the service with repository and current user.
        
        Args:
            equipment_repository: Equipment repository dependency
            current_user: Current authenticated user
        """
        self.equipment_repository = equipment_repository
        self.current_user = current_user
    
    def create_equipment(self, equipment_data: MasterEquipmentCreate) -> MasterEquipmentResponse:
        """Create a new master equipment record.
        
        Args:
            equipment_data: Equipment data to create
            
        Returns:
            Created equipment response
            
        Raises:
            HTTPException: If creation fails
        """
        try:
            equipment = self.equipment_repository.create(equipment_data)
            return MasterEquipmentResponse.model_validate(equipment)
        except Exception as e:
            log_error_with_context(
                  error=e,
                  context={
                      "operation": "create_equipment",
                      "equipment_data": equipment_data.model_dump()
                  }
              )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create equipment"
            )
    
    def get_equipment_by_id(self, equipment_id: int) -> MasterEquipmentResponse:
        """Get equipment by ID.
        
        Args:
            equipment_id: Equipment ID
            
        Returns:
            Equipment response
            
        Raises:
            HTTPException: If equipment not found
        """
        try:
            equipment = self.equipment_repository.get_by_id(equipment_id)
            if not equipment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Equipment not found"
                )
            
            return MasterEquipmentResponse.model_validate(equipment)
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_equipment_by_id",
                    "equipment_id": equipment_id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve equipment"
            )
        
    
    def get_all_equipment(self, skip: int = 0, limit: int = 100) -> List[MasterEquipmentResponse]:
        """Get all equipment records with pagination.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of equipment responses
        """
        try:
            equipment_list = self.equipment_repository.get_all(skip=skip, limit=limit)
            return [MasterEquipmentResponse.model_validate(equipment) for equipment in equipment_list]
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "get_all_equipment",
                    "skip": skip,
                    "limit": limit
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve equipment"
            )
    
    def update_equipment(self, equipment_id: int, equipment_data: MasterEquipmentUpdate) -> MasterEquipmentResponse:
        """Update an existing equipment record.
        
        Args:
            equipment_id: Equipment ID to update
            equipment_data: Updated equipment data
            
        Returns:
            Updated equipment response
            
        Raises:
            HTTPException: If equipment not found or update fails
        """
        try:
            if not self.equipment_repository.check_if_exists(equipment_id):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Equipment not found"
                )
            
            equipment = self.equipment_repository.update(equipment_id, equipment_data)
            if not equipment:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to update equipment"
                )
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "update_equipment",
                    "equipment_id": equipment_id,
                    "equipment_data": equipment_data.model_dump()
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update equipment"
            )
        
        return MasterEquipmentResponse.model_validate(equipment)
    
    def delete_equipment(self, equipment_id: int) -> dict[str, str]:
        """Delete an equipment record.
        
        Args:
            equipment_id: Equipment ID to delete
            
        Returns:
            Success message
            
        Raises:
            HTTPException: If equipment not found or deletion fails
        """
        try:
            if not self.equipment_repository.check_if_exists(equipment_id):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Equipment not found"
                )
            
            success = self.equipment_repository.delete(equipment_id)
            if not success:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to delete equipment"
                )
        except Exception as e:
            log_error_with_context(
                error=e,
                context={
                    "operation": "delete_equipment",
                    "equipment_id": equipment_id
                }
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete equipment"
            )

        success = self.equipment_repository.delete(equipment_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete equipment"
            )
        
        return {"message": "Equipment deleted successfully"}
