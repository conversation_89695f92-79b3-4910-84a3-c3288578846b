from typing import Annotated
from fastapi import Depends
from app.core.jwt import get_current_user
from app.db import User
from app.repositories import UserRepository


class ProfileService:
    def __init__(
        self, 
        user_repository: Annotated[UserRepository, Depends()],
        current_user: Annotated[User, Depends(get_current_user)]
    ):
        self.user_repository = user_repository
        self.current_user = current_user

    def get_profile(self) -> User:
        if not self.current_user.id:
            raise ValueError("User ID is None")
        return self.user_repository.get_by_id(self.current_user.id)
